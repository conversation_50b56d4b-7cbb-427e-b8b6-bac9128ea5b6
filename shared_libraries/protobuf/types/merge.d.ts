export declare function mergeCacheObject(to: Object, from: Object, objType?: string): void;
export declare function mergeUserObject(to: Object, from: Object): void;
export declare function mergeBoardObject(to: Object, from: Object): void;
export declare function mergeGroupObject(to: Object, from: Object): void;
export declare function mergeSessionObject(to: Object, from: Object): void;
//# sourceMappingURL=merge.d.ts.map