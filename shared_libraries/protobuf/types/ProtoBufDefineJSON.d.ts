export declare const ProtoBufDefineJSON: {
    nested: {
        AccessToken: {
            edition: string;
            fields: {
                board_id: {
                    type: string;
                    id: number;
                };
                device_id: {
                    type: string;
                    id: number;
                };
                client_id: {
                    type: string;
                    id: number;
                };
                agent_id: {
                    type: string;
                    id: number;
                };
                access_token: {
                    type: string;
                    id: number;
                };
                access_token_expire_timestamp: {
                    type: string;
                    id: number;
                };
                access_token_created_timestamp: {
                    type: string;
                    id: number;
                };
                uid: {
                    type: string;
                    id: number;
                };
                token: {
                    type: string;
                    id: number;
                };
                token_from_cookie: {
                    type: string;
                    id: number;
                };
                session_id: {
                    type: string;
                    id: number;
                };
                session_id_from_cookie: {
                    type: string;
                    id: number;
                };
                connection_id: {
                    type: string;
                    id: number;
                };
                email: {
                    type: string;
                    id: number;
                };
                client_ip: {
                    type: string;
                    id: number;
                };
                server_ip: {
                    type: string;
                    id: number;
                };
                awselb: {
                    type: string;
                    id: number;
                };
                request_url: {
                    type: string;
                    id: number;
                };
                host: {
                    type: string;
                    id: number;
                };
                referer: {
                    type: string;
                    id: number;
                };
                client_private_ip: {
                    type: string;
                    id: number;
                };
                origin: {
                    type: string;
                    id: number;
                };
                connection_number: {
                    type: string;
                    id: number;
                };
                client_ua: {
                    type: string;
                    id: number;
                };
                client_version: {
                    type: string;
                    id: number;
                };
                client_accept_language: {
                    type: string;
                    id: number;
                };
                gid: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                x_forwarded_uri: {
                    type: string;
                    id: number;
                };
                token_verified: {
                    type: string;
                    id: number;
                };
                agent_token_verified: {
                    type: string;
                    id: number;
                };
                server_token_verified: {
                    type: string;
                    id: number;
                };
                websocket: {
                    type: string;
                    id: number;
                };
                board_access_token_verified: {
                    type: string;
                    id: number;
                };
                scope: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
            };
        };
        TransactionLog: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                message: {
                    type: string;
                    id: number;
                };
            };
        };
        ClientRequestType: {
            edition: string;
            options: {
                allow_alias: boolean;
            };
            values: {
                INVALID_REQUEST: number;
                CLIENT_REQUEST_CONNECT: number;
                JOB_REQUEST_CONNECT: number;
                CLIENT_REQUEST_PING: number;
                USER_REQUEST_SSO: number;
                USER_REQUEST_READ_SSO_OPTIONS: number;
                USER_REQUEST_REGISTER: number;
                USER_REQUEST_REGISTER_AGENT: number;
                USER_REQUEST_UNREGISTER_AGENT: number;
                USER_REQUEST_RESEND_VERIFICATION_EMAIL: number;
                USER_REQUEST_VERIFY_EMAIL_TOKEN: number;
                USER_REQUEST_ENTER_FOREGROUND: number;
                USER_REQUEST_RESEND_VERIFICATION_CODE: number;
                USER_REQUEST_RESEND_VERIFICATION_CODE_EMAIL: number;
                USER_REQUEST_VERIFY_CODE: number;
                USER_REQUEST_VERIFY_EMAIL_CODE: number;
                USER_REQUEST_READ: number;
                USER_REQUEST_READ_CAP: number;
                USER_REQUEST_UNREAD_FEEDS_COUNT: number;
                USER_REQUEST_SUBSCRIBE: number;
                USER_REQUEST_READ_FEEDS: number;
                USER_REQUEST_READ_NOTES: number;
                USER_REQUEST_UPDATE: number;
                USER_REQUEST_UPDATE_PICTURES: number;
                USER_REQUEST_UPDATE_NAME: number;
                USER_REQUEST_UPDATE_PHONE_NUMBER: number;
                USER_REQUEST_UPDATE_EMAIL: number;
                USER_REQUEST_UPDATE_USER_BOARD: number;
                USER_REQUEST_UPDATE_USER_BOARD_ENTER: number;
                USER_REQUEST_UPDATE_USER_GROUP: number;
                USER_REQUEST_UPDATE_AGENT: number;
                USER_REQUEST_LOGIN: number;
                USER_REQUEST_RESOURCE_TOKEN: number;
                USER_REQUEST_ACCESS_TOKEN: number;
                USER_REQUEST_VERIFY_TOKEN: number;
                USER_REQUEST_DUPLICATE_TOKEN: number;
                USER_REQUEST_VERIFY_PASSWORD: number;
                USER_REQUEST_REFRESH_TOKEN: number;
                USER_REQUEST_LOGOUT: number;
                USER_REQUEST_LOGOUT_ALL_DEVICES: number;
                USER_REQUEST_UPLOAD_RESOURCE: number;
                USER_REQUEST_UPLOAD_PROFILE_PICTURES: number;
                USER_REQUEST_DOWNLOAD_RESOURCE: number;
                USER_REQUEST_RESET_PASSWORD: number;
                USER_REQUEST_CHANGE_PASSWORD: number;
                USER_REQUEST_CATEGORY_CREATE: number;
                USER_REQUEST_CATEGORY_RENAME: number;
                USER_REQUEST_CATEGORY_DELETE: number;
                USER_REQUEST_CATEGORY_ASSIGN: number;
                USER_REQUEST_READ_SESSIONS: number;
                USER_REQUEST_READ_BOARDS: number;
                USER_REQUEST_READ_RELATIONS: number;
                USER_REQUEST_READ_AUTO_ARCHIVED_BOARDS: number;
                USER_REQUEST_READ_USER_BOARDS: number;
                USER_REQUEST_EMAIL_LOOKUP: number;
                USER_REQUEST_PHONE_NUMBER_LOOKUP: number;
                USER_REQUEST_BOARD_LOOKUP: number;
                USER_REQUEST_RELATION_LOOKUP: number;
                USER_REQUEST_REMOVE_FAVORITE: number;
                USER_REQUEST_REMOVE_MENTIONME: number;
                USER_REQUEST_REMOVE_MENTIONME_BEFORE: number;
                USER_REQUEST_REMOVE_NOTIFICATION: number;
                USER_REQUEST_REMOVE_NOTIFICATION_BEFORE: number;
                USER_REQUEST_UPDATE_ORDER_NUMBER: number;
                USER_REQUEST_UPDATE_ACTION_ITEM: number;
                USER_REQUEST_REGISTER_LOCAL_USER: number;
                USER_REQUEST_LOGIN_LOCAL_USER: number;
                USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN: number;
                USER_REQUEST_VERIFY_LOCAL_EMAIL_CODE: number;
                USER_REQUEST_CREATE_RELATION_VIA_QR_TOKEN: number;
                USER_REQUEST_PREVIEW_EMAIL_TOKEN: number;
                USER_REQUEST_REGISTER_LOCAL_USER_BY_APPLE_ID: number;
                USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_APPLE_ID: number;
                USER_REQUEST_LOGIN_LOCAL_USER_BY_APPLE_ID: number;
                USER_REQUEST_REGISTER_LOCAL_USER_BY_GOOGLE_ID: number;
                USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_GOOGLE_ID: number;
                USER_REQUEST_LOGIN_LOCAL_USER_BY_GOOGLE_ID: number;
                USER_REQUEST_REGISTER_LOCAL_USER_BY_VERIFICATION_CODE: number;
                USER_REQUEST_REGISTER_LOCAL_USER_WITH_QR_TOKEN_BY_VERIFICATION_CODE: number;
                USER_REQUEST_LOGIN_LOCAL_USER_BY_VERIFICATION_CODE: number;
                USER_REQUEST_LOGIN_LOCAL_USER_BY_GROUP_INVITATION_TOKEN: number;
                USER_REQUEST_LOGIN_LOCAL_USER_BY_SALESFORCE_ID: number;
                USER_REQUEST_UPDATE_DEVICE_TOKEN: number;
                USER_REQUEST_SEARCH: number;
                USER_REQUEST_SEARCH_CONTENT_LIBRARY_BOARD: number;
                USER_REQUEST_SEARCH_FLOW_TEMPLATE_LIBRARY_BOARD: number;
                USER_REQUEST_KEEP_ALIVE: number;
                USER_REQUEST_EMAIL_AUTH: number;
                USER_REQUEST_EMAIL_DELIVERY: number;
                USER_REQUEST_CONTACT_INVITE: number;
                USER_REQUEST_CONTACT_ACCEPT: number;
                USER_REQUEST_CONTACT_DENY: number;
                USER_REQUEST_CONTACT_CANCEL: number;
                USER_REQUEST_CONTACT_VIEW_INVITATION: number;
                USER_REQUEST_CREATE_CONTACT: number;
                USER_REQUEST_UPDATE_CONTACT: number;
                USER_REQUEST_DELETE_CONTACT: number;
                USER_REQUEST_DELETE_RESOURCE: number;
                USER_REQUEST_FEEDBACK: number;
                USER_REQUEST_FEEDBACK_AND_ATTACHMENT: number;
                USER_REQUEST_SYSTEM_FEEDBACK: number;
                USER_REQUEST_SSO_REGISTERED_USER: number;
                USER_REQUEST_SSO_GROUP_UNIQUE_ID: number;
                USER_REQUEST_SSO_EXTERNAL_MOXTRA: number;
                USER_REQUEST_SSO_REDIRECT: number;
                USER_REQUEST_UPDATE_SIP_REGISTRATION_STATUS: number;
                USER_REQUEST_CREATE_CALL_LOG: number;
                USER_REQUEST_UPDATE_CALL_LOG: number;
                USER_REQUEST_DELETE_CALL_LOG: number;
                USER_REQUEST_READ_PASSWORD_RULE: number;
                USER_REQUEST_RESEND_DELETION_EMAIL: number;
                USER_REQUEST_VERIFY_DELETION_TOKEN: number;
                USER_REQUEST_DELETE: number;
                USER_REQUEST_DELETE_LOCAL_USER: number;
                USER_REQUEST_FOLLOW_GROUP_BOARD: number;
                USER_REQUEST_UNFOLLOW_GROUP_BOARD: number;
                USER_REQUEST_UPDATE_GROUP_BOARD: number;
                USER_REQUEST_UPDATE_OUT_OF_OFFICE: number;
                USER_REQUEST_POST_ACTIVITY: number;
                USER_REQUEST_READ_ACTIVITY: number;
                USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_EMAIL: number;
                USER_REQUEST_QR_TOKEN: number;
                USER_REQUEST_VIEW_QR_TOKEN: number;
                USER_REQUEST_RESEND_LOCAL_VERIFICATION_CODE_SMS: number;
                USER_REQUEST_VERIFY_LOCAL_SMS_CODE: number;
                USER_REQUEST_VERIFY_LOCAL_APPLE_JWT: number;
                USER_REQUEST_VERIFY_LOCAL_GOOGLE_JWT: number;
                USER_REQUEST_RESEND_APP_DOWNLOAD_LINK_SMS: number;
                USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_EMAIL: number;
                USER_REQUEST_GLOBAL_LOOKUP_DOMAIN_BY_PHONE_NUMBER: number;
                USER_REQUEST_GLOBAL_RESEND_SMS_CODE: number;
                USER_REQUEST_GLOBAL_VERIFY_SMS_CODE: number;
                USER_REQUEST_GLOBAL_RESEND_EMAIL_CODE: number;
                USER_REQUEST_GLOBAL_VERIFY_EMAIL_CODE: number;
                USER_REQUEST_PUSH_NOTIFICATION: number;
                USER_REQUEST_SMS: number;
                USER_REQUEST_CREATE_BROADCAST: number;
                USER_REQUEST_UPDATE_BROADCAST: number;
                USER_REQUEST_DELETE_BROADCAST: number;
                USER_REQUEST_GET_HTML: number;
                USER_REQUEST_SHORT_URL: number;
                USER_REQUEST_LOOKUP_SHORT_URL: number;
                USER_REQUEST_VERIFY_GOOGLE_PLAY_INTEGRITY_TOKEN: number;
                USER_REQUEST_USER_BOARD_ARCHIVE: number;
                USER_REQUEST_MAX: number;
                BOARD_REQUEST_CREATE: number;
                BOARD_REQUEST_DUPLICATE: number;
                BOARD_REQUEST_READ: number;
                BOARD_REQUEST_VIEW: number;
                BOARD_REQUEST_VIEW_AS_OGO: number;
                BOARD_REQUEST_READ_FEEDS: number;
                BOARD_REQUEST_SUBSCRIBE: number;
                BOARD_REQUEST_UNSUBSCRIBE: number;
                BOARD_REQUEST_SUBSCRIBE_MULTIPLE: number;
                BOARD_REQUEST_UPDATE: number;
                BOARD_REQUEST_COPY_PAGES: number;
                BOARD_REQUEST_COPY_RESOURCES: number;
                BOARD_REQUEST_COPY_TODOS: number;
                BOARD_REQUEST_CREATE_COMMENT: number;
                BOARD_REQUEST_UPLOAD_COMMENT: number;
                BOARD_REQUEST_UPDATE_COMMENT: number;
                BOARD_REQUEST_DELETE_COMMENT: number;
                BOARD_REQUEST_TYPE_INDICATION: number;
                BOARD_REQUEST_DELETE: number;
                BOARD_REQUEST_UPDATE_COMMENT_URL_PREVIEW: number;
                BOARD_REQUEST_DELETE_COMMENT_URL_PREVIEW: number;
                BOARD_REQUEST_INCREASE_USED_COUNT: number;
                BOARD_REQUEST_UPDATE_BOARD_USER: number;
                BOARD_REQUEST_UPLOAD_RESOURCE: number;
                BOARD_REQUEST_UPLOAD_AUDIO: number;
                BOARD_REQUEST_DOWNLOAD_RESOURCE: number;
                BOARD_REQUEST_DOWNLOAD_BOARD: number;
                BOARD_REQUEST_UPLOAD_RESOURCE_URL: number;
                BOARD_REQUEST_DOWNLOAD_FOLDER: number;
                BOARD_REQUEST_DOWNLOAD_ZIP: number;
                BOARD_REQUEST_INVITE: number;
                BOARD_REQUEST_JOIN: number;
                BOARD_REQUEST_LEAVE: number;
                BOARD_REQUEST_APPROVE: number;
                BOARD_REQUEST_DENY: number;
                BOARD_REQUEST_EXPEL: number;
                BOARD_REQUEST_SET_ACCESS_TYPE: number;
                BOARD_REQUEST_VIEW_INVITATION: number;
                BOARD_REQUEST_INVITE_OUT_OF_OFFICE_BACKUP_USER: number;
                BOARD_REQUEST_ACTION_TRANSFER: number;
                BOARD_REQUEST_GET_RECORDINGS: number;
                BOARD_REQUEST_JOIN_BY_VIEW_TOKEN: number;
                BOARD_REQUEST_UPDATE_REQUESTING_USER: number;
                BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_APPLE_ID: number;
                BOARD_REQUEST_JOIN_BY_VIEW_TOKEN_GOOGLE_ID: number;
                BOARD_REQUEST_CREATE_VIEW_TOKEN: number;
                BOARD_REQUEST_UPDATE_VIEW_TOKEN: number;
                BOARD_REQUEST_REMOVE_VIEW_TOKEN: number;
                BOARD_REQUEST_EMAIL_VIEW_TOKEN: number;
                BOARD_REQUEST_READ_VIEW_TOKEN: number;
                BOARD_REQUEST_READ_VIEW_TOKENS: number;
                BOARD_REQUEST_SEARCH_BOARD: number;
                BOARD_REQUEST_SEARCH_GROUP_BOARD: number;
                BOARD_REQUEST_SEARCH_GROUP: number;
                SESSION_REQUEST_START: number;
                SESSION_REQUEST_RESTART: number;
                SESSION_REQUEST_END: number;
                SESSION_REQUEST_JOIN: number;
                SESSION_REQUEST_SUBSCRIBE: number;
                SESSION_REQUEST_LEAVE: number;
                SESSION_REQUEST_INVITE: number;
                SESSION_REQUEST_KEEP_ALIVE: number;
                SESSION_REQUEST_EVENT_LOG: number;
                SESSION_REQUEST_READ_EVENT_LOG: number;
                SESSION_REQUEST_WEBRTC_OFFER: number;
                SESSION_REQUEST_CHANGE_PRESENTER: number;
                SESSION_REQUEST_START_DS: number;
                SESSION_REQUEST_STOP_DS: number;
                SESSION_REQUEST_PUBLISH_DS_STATE: number;
                SESSION_REQUEST_PUBLISH_AUDIO_STATE: number;
                SESSION_REQUEST_PUBLISH_VIDEO_STATE: number;
                SESSION_REQUEST_READ: number;
                SESSION_REQUEST_READ_ROSTER: number;
                SESSION_REQUEST_UPDATE_AUDIO_STATUS: number;
                SESSION_REQUEST_UPDATE_VIDEO_STATUS: number;
                SESSION_REQUEST_START_SESSION: number;
                SESSION_REQUEST_RECLAIM_HOST: number;
                SESSION_REQUEST_SET_PRESENTER: number;
                SESSION_REQUEST_SET_HOST: number;
                SESSION_REQUEST_MUTE: number;
                SESSION_REQUEST_UNMUTE: number;
                SESSION_REQUEST_MUTE_ALL: number;
                SESSION_REQUEST_SWITCH_PAGE: number;
                SESSION_REQUEST_START_DESKTOPSHARE: number;
                SESSION_REQUEST_LEAVE_TELEPHONY: number;
                SESSION_REQUEST_SCHEDULE: number;
                SESSION_REQUEST_DOWNLOAD_CALENDAR: number;
                SESSION_REQUEST_CREATE_PERSONAL_ROOM: number;
                SESSION_REQUEST_REMOVE_ROSTER: number;
                SESSION_REQUEST_LOCK: number;
                SESSION_REQUEST_UNLOCK: number;
                SESSION_REQUEST_SCHEDULE_TO_BOARD_USER: number;
                BOARD_REQUEST_UPDATE_BOARD: number;
                BOARD_REQUEST_UPLOAD_BOARD_RESOURCE: number;
                BOARD_REQUEST_CREATE_PAGE: number;
                BOARD_REQUEST_UPLOAD_PAGE: number;
                BOARD_REQUEST_UPDATE_PAGE: number;
                BOARD_REQUEST_DELETE_PAGE: number;
                BOARD_REQUEST_DELETE_RESOURCE: number;
                BOARD_REQUEST_SET_EDITOR: number;
                BOARD_REQUEST_REMOVE_EDITOR: number;
                BOARD_REQUEST_SET_EDITOR_TYPE: number;
                BOARD_REQUEST_CREATE_PAGE_ELEMENT: number;
                BOARD_REQUEST_UPLOAD_PAGE_ELEMENT: number;
                BOARD_REQUEST_UPDATE_PAGE_ELEMENT: number;
                BOARD_REQUEST_DELETE_PAGE_ELEMENT: number;
                BOARD_REQUEST_CREATE_PAGE_COMMENT: number;
                BOARD_REQUEST_UPLOAD_PAGE_COMMENT: number;
                BOARD_REQUEST_UPDATE_PAGE_COMMENT: number;
                BOARD_REQUEST_DELETE_PAGE_COMMENT: number;
                BOARD_REQUEST_CREATE_PAGE_POSITION_COMMENT: number;
                BOARD_REQUEST_UPLOAD_PAGE_POSITION_COMMENT: number;
                BOARD_REQUEST_UPDATE_PAGE_POSITION_COMMENT: number;
                BOARD_REQUEST_DELETE_PAGE_POSITION_COMMENT: number;
                BOARD_REQUEST_CREATE_PAGE_GROUP: number;
                BOARD_REQUEST_UPDATE_PAGE_GROUP: number;
                BOARD_REQUEST_DELETE_PAGE_GROUP: number;
                BOARD_REQUEST_COPY_PAGE_GROUP: number;
                BOARD_REQUEST_MOVE_PAGE_GROUP: number;
                BOARD_REQUEST_COPY_SIGNATURE: number;
                BOARD_REQUEST_CREATE_PAGE_TAG: number;
                BOARD_REQUEST_UPDATE_PAGE_TAG: number;
                BOARD_REQUEST_DELETE_PAGE_TAG: number;
                BOARD_REQUEST_DOWNLOAD_NOTE: number;
                BOARD_REQUEST_CREATE_TODO: number;
                BOARD_REQUEST_UPLOAD_TODO: number;
                BOARD_REQUEST_UPDATE_TODO: number;
                BOARD_REQUEST_DELETE_TODO: number;
                BOARD_REQUEST_SET_TODO_ASSIGNEE: number;
                BOARD_REQUEST_SET_TODO_DUE_DATE: number;
                BOARD_REQUEST_SET_TODO_COMPLETED: number;
                BOARD_REQUEST_UPDATE_TODO_ATTACHMENT: number;
                BOARD_REQUEST_UPLOAD_TODO_COMMENT: number;
                BOARD_REQUEST_CREATE_TODO_COMMENT: number;
                BOARD_REQUEST_UPDATE_TODO_COMMENT: number;
                BOARD_REQUEST_DELETE_TODO_COMMENT: number;
                BOARD_REQUEST_CREATE_TODO_REMINDER: number;
                BOARD_REQUEST_UPDATE_TODO_REMINDER: number;
                BOARD_REQUEST_DELETE_TODO_REMINDER: number;
                BOARD_REQUEST_DELETE_TODO_FILE: number;
                BOARD_REQUEST_READ_FLAT_FEEDS: number;
                BOARD_REQUEST_READ_THREAD: number;
                BOARD_REQUEST_READ_ONGOING_SIGNATURES: number;
                BOARD_REQUEST_READ_ONGOING_TRANSACTIONS: number;
                BOARD_REQUEST_READ_ONGOING_DELEGATE_FEEDS: number;
                BOARD_REQUEST_READ_COVER: number;
                BOARD_REQUEST_LIST_FOLDER: number;
                BOARD_REQUEST_READ_FILE: number;
                BOARD_REQUEST_LIST_SIGNATURES: number;
                BOARD_REQUEST_LIST_TODOS: number;
                BOARD_REQUEST_READ_SIGNATURE: number;
                BOARD_REQUEST_CREATE_REMINDER: number;
                BOARD_REQUEST_UPDATE_REMINDER: number;
                BOARD_REQUEST_DELETE_REMINDER: number;
                BOARD_REQUEST_READ_AUDIT_OBJECT: number;
                BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_RESOURCE: number;
                BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_BOARD: number;
                BOARD_REQUEST_AUDIT_OBJECT_DOWNLOAD_ZIP: number;
                BOARD_REQUEST_RESEND_INVITATION_EMAIL: number;
                BOARD_REQUEST_RESEND_INVITATION_SMS: number;
                BOARD_REQUEST_CREATE_INVITATION_VIEW_TOKEN: number;
                BOARD_REQUEST_RESEND_VIEW_TOKEN: number;
                BOARD_REQUEST_RENEW_WORKSPACE_ID: number;
                BOARD_REQUEST_RESET_INVITE_CODE: number;
                BOARD_PUBLISH_ACTION: number;
                OBSOLETE_BOARD_REQUEST_SET_EMAIL_ADDRESS: number;
                BOARD_REQUEST_SET_PHONE_NUMBER: number;
                BOARD_REQUEST_CREATE_FOLDER: number;
                BOARD_REQUEST_UPDATE_FOLDER: number;
                BOARD_REQUEST_DELETE_FOLDER: number;
                BOARD_REQUEST_COPY_FOLDER: number;
                BOARD_REQUEST_MOVE_FOLDER: number;
                BOARD_REQUEST_CREATE_FAVORITE: number;
                BOARD_REQUEST_PIN: number;
                BOARD_REQUEST_CHECK_ISRESTRICT: number;
                BOARD_REQUEST_CALL_LOG: number;
                BOARD_REQUEST_SET_OWNER_DELEGATE: number;
                BOARD_REQUEST_SET_FEED_STATUS: number;
                BOARD_REQUEST_SET_OWNER: number;
                BOARD_REQUEST_CREATE_SIGNATURE: number;
                BOARD_REQUEST_UPDATE_SIGNATURE: number;
                BOARD_REQUEST_DELETE_SIGNATURE: number;
                BOARD_REQUEST_ADD_SIGNATURE_SIGNEE: number;
                BOARD_REQUEST_UPDATE_SIGNATURE_SIGNEE: number;
                BOARD_REQUEST_REMOVE_SIGNATURE_SIGNEE: number;
                BOARD_REQUEST_CREATE_SIGNATURE_PAGE_ELEMENT: number;
                BOARD_REQUEST_UPLOAD_SIGNATURE_PAGE_ELEMENT: number;
                BOARD_REQUEST_UPDATE_SIGNATURE_PAGE_ELEMENT: number;
                BOARD_REQUEST_DELETE_SIGNATURE_PAGE_ELEMENT: number;
                BOARD_REQUEST_START_SIGNATURE: number;
                BOARD_REQUEST_UPLOAD_SIGNATURE_RESOURCE: number;
                BOARD_REQUEST_SUBMIT_SIGNATURE: number;
                BOARD_REQUEST_VIEW_SIGNATURE: number;
                BOARD_REQUEST_SIGNEE_UPDATE: number;
                BOARD_REQUEST_SIGNEE_UPLOAD_RESOURCE: number;
                BOARD_REQUEST_DOWNLOAD_SIGNATURE_RESOURCE: number;
                BOARD_REQUEST_SIGNATURE_UPDATE_ATTACHMENT: number;
                BOARD_REQUEST_SIGNATURE_DELETE_ATTACHMENT: number;
                BOARD_REQUEST_SIGNATURE_REOPEN: number;
                BOARD_REQUEST_SIGNATURE_RESET_STATUS: number;
                BOARD_REQUEST_SIGNATURE_REPLACE: number;
                BOARD_REQUEST_SIGNATURE_REPLACE_PAGES_FROM_FILE: number;
                BOARD_REQUEST_CREATE_WEBAPP_TOKEN: number;
                BOARD_REQUEST_WEBAPP_CALLBACK: number;
                FILE_FLOW_REQUEST_COMMENT_CREATE: number;
                FILE_FLOW_REQUEST_COMMENT_UPDATE: number;
                FILE_FLOW_REQUEST_COMMENT_UPLOAD: number;
                FILE_FLOW_REQUEST_COMMENT_DELETE: number;
                SESSION_FLOW_REQUEST_COMMENT_CREATE: number;
                SESSION_FLOW_REQUEST_COMMENT_UPDATE: number;
                SESSION_FLOW_REQUEST_COMMENT_UPLOAD: number;
                SESSION_FLOW_REQUEST_COMMENT_DELETE: number;
                SIGN_FLOW_REQUEST_COMMENT_CREATE: number;
                SIGN_FLOW_REQUEST_COMMENT_UPDATE: number;
                SIGN_FLOW_REQUEST_COMMENT_UPLOAD: number;
                SIGN_FLOW_REQUEST_COMMENT_DELETE: number;
                BOARD_REQUEST_CREATE_WAITING_USER: number;
                BOARD_REQUEST_UPDATE_WAITING_USER: number;
                BOARD_REQUEST_DELETE_WAITING_USER: number;
                BOARD_REQUEST_UPDATE_RESOURCE: number;
                BOARD_REQUEST_TRANSACTION_CREATE: number;
                BOARD_REQUEST_TRANSACTION_DELETE: number;
                BOARD_REQUEST_TRANSACTION_UPDATE: number;
                BOARD_REQUEST_TRANSACTION_COPY: number;
                BOARD_REQUEST_TRANSACTION_READ: number;
                BOARD_REQUEST_TRANSACTION_STEP_SUBMIT: number;
                BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT: number;
                BOARD_REQUEST_TRANSACTION_UPDATE_ATTACHMENT: number;
                BOARD_REQUEST_TRANSACTION_DELETE_ATTACHMENT: number;
                BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_RESOURCE: number;
                BOARD_REQUEST_TRANSACTION_COMMENT_CREATE: number;
                BOARD_REQUEST_TRANSACTION_COMMENT_UPDATE: number;
                BOARD_REQUEST_TRANSACTION_COMMENT_DELETE: number;
                BOARD_REQUEST_TRANSACTION_COMMENT_UPLOAD: number;
                BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_FILE: number;
                BOARD_REQUEST_TRANSACTION_UPLOAD_ATTACHMENT_FROM_URL: number;
                BOARD_REQUEST_TRANSACTION_STEP_SUBMIT_BATCH: number;
                BOARD_REQUEST_TRANSACTION_VIEW: number;
                BOARD_REQUEST_TRANSACTION_RESET_STATUS: number;
                BOARD_REQUEST_TRANSACTION_REOPEN: number;
                BOARD_REQUEST_TRANSACTION_STEP_REOPEN: number;
                BOARD_REQUEST_TRANSACTION_DELETE_BATCH: number;
                BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE: number;
                BOARD_REQUEST_TRANSACTION_UPLOAD_RESOURCE_URL: number;
                BOARD_REQUEST_TRANSACTION_DOWNLOAD_RESOURCE: number;
                BOARD_REQUEST_TRANSACTION_REMINDER_CREATE: number;
                BOARD_REQUEST_TRANSACTION_REMINDER_UPDATE: number;
                BOARD_REQUEST_TRANSACTION_REMINDER_DELETE: number;
                BOARD_REQUEST_SET_FEED_UNREAD: number;
                BOARD_REQUEST_SET_ISTEMP_OFF: number;
                BOARD_REQUEST_UPDATE_RSVP: number;
                BOARD_REQUEST_CREATE_PIN: number;
                BOARD_REQUEST_DELETE_PIN: number;
                BOARD_REQUEST_READ_FLAT_PINS: number;
                BOARD_REQUEST_SET_BOARD_TYPE: number;
                BOARD_REQUEST_SET_ACTIVE: number;
                BOARD_REQUEST_WORKFLOW_CREATE: number;
                BOARD_REQUEST_WORKFLOW_UPDATE: number;
                BOARD_REQUEST_WORKFLOW_DELETE: number;
                BOARD_REQUEST_READ_PREDECESSORS: number;
                WORKFLOW_REQUEST_CREATE_TEMPLATE: number;
                WORKFLOW_REQUEST_UPDATE_TEMPLATE: number;
                WORKFLOW_REQUEST_DELETE_TEMPLATE: number;
                WORKFLOW_REQUEST_LIST_TEMPLATE: number;
                WORKFLOW_REQUEST_USE_TEMPLATE: number;
                WORKFLOW_REQUEST_COPY_TEMPLATE: number;
                WORKFLOW_REQUEST_LIST_PREBUILT_TEMPLATE: number;
                WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW: number;
                WORKFLOW_REQUEST_UPDATE_BOARD_WORKFLOW_STEP: number;
                WORKFLOW_REQUEST_SKIP_BOARD_WORKFLOW_STEP: number;
                WORKFLOW_REQUEST_CREATE_WORKFLOW: number;
                WORKFLOW_REQUEST_UPDATE_WORKFLOW: number;
                WORKFLOW_REQUEST_DELETE_WORKFLOW: number;
                WORKFLOW_REQUEST_UPDATE_STATUS: number;
                WORKFLOW_REQUEST_RESTART: number;
                WORKFLOW_REQUEST_COMPLETE: number;
                WORKFLOW_REQUEST_CANCEL: number;
                WORKFLOW_REQUEST_REOPEN_STEP: number;
                WORKFLOW_REQUEST_SKIP_STEP: number;
                WORKFLOW_REQUEST_INCREASE_USED_COUNT: number;
                WORKFLOW_REQUEST_COPY_AS_TEMPLATE: number;
                WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_FOLDER_COUNT: number;
                WORKFLOW_REQUEST_COPY_WORKFLOW: number;
                WORKFLOW_REQUEST_READ_TEMPLATE_LIBRARY_TEMPLATE_COUNT: number;
                WORKFLOW_REQUEST_MOVE_TEMPLATE: number;
                BOARD_REQUEST_CREATE_PROPERTY: number;
                BOARD_REQUEST_UPDATE_PROPERTY: number;
                BOARD_REQUEST_DELETE_PROPERTY: number;
                BOARD_REQUEST_UPDATE_FEED_REACTION: number;
                BOARD_REQUEST_CREATE_BROADCAST: number;
                BOARD_REQUEST_UPDATE_BROADCAST: number;
                BOARD_REQUEST_DELETE_BROADCAST: number;
                BOARD_REQUEST_UPDATE_MEETING_TRANSCRIPTION: number;
                BOARD_REQUEST_DOWNLOAD_MEETING_TRANSCRIPTION: number;
                BOARD_REQUEST_COPY_MEETING_TRANSCRIPTION: number;
                BOARD_REQUEST_TRANSCRIPT_RESOURCE: number;
                BOARD_REQUEST_UPDATE_MEETING_SUMMARY: number;
                BOARD_REQUEST_UNITTEST: number;
                BOARD_REQUEST_MAX: number;
                SERVER_AUDIO_CAPACITY: number;
                SERVER_PBX_REPORT: number;
                SERVER_DESKTOP_SHARE_CAPACITY: number;
                SERVER_PROBE: number;
                SERVER_OBJECT_READ: number;
                SERVER_OBJECT_SUBSCRIBE: number;
                SERVER_OBJECT_UNSUBSCRIBE: number;
                SERVER_OBJECT_ACTIVITY: number;
                SERVER_OBJECT_AUDIT: number;
                SERVER_OBJECT_WRITE: number;
                SERVER_OBJECT_STATS: number;
                SERVER_OBJECT_LIST_SERVERS: number;
                SERVER_FILE_DOWNLOAD: number;
                SERVER_FILE_UPLOAD: number;
                SERVER_FILE_PREVIEW: number;
                SERVER_USER_DISABLE: number;
                SERVER_USER_ENABLE: number;
                SERVER_USER_LEVEL_UPGRADE: number;
                SERVER_USER_LEVEL_DOWNGRADE: number;
                SERVER_USER_READ: number;
                SERVER_USER_UP_SIZE: number;
                SERVER_USER_DOWN_SIZE: number;
                SERVER_REDO_JOB: number;
                SERVER_FORWARD_REQUEST: number;
                SERVER_UPLOAD_CRASH_REPORT: number;
                SERVER_LIST_CRASH_REPORTS: number;
                SERVER_DOWNLOAD_CRASH_REPORT: number;
                SERVER_DELETE_CRASH_REPORT: number;
                SERVER_READ_STATISTICS: number;
                SERVER_UPDATE_STATISTICS: number;
                SERVER_TOKEN_DECODE: number;
                SERVER_SERVICE_PROVIDERS_READ: number;
                SERVER_SERVICE_PROVIDERS_UPDATE: number;
                SERVER_IDP_CONFIG_READ: number;
                SERVER_WEBAPP_READ: number;
                SERVER_SYSTEM_CONFIG_READ: number;
                SERVER_SYSTEM_CONFIG_UPDATE: number;
                SERVER_REQUEST_VALIDATION_CODE: number;
                SERVER_GROUP_LIST_ADD: number;
                SERVER_GROUP_LIST_REMOVE: number;
                SERVER_CHART_READ: number;
                SERVER_GROUP_USAGE_REPORT: number;
                SERVER_PROBE_REPORT: number;
                SERVER_STATISTICS_REPORT: number;
                SERVER_REQUEST_MAX: number;
                AGENT_REQUEST_LIST_FOLDER: number;
                AGENT_REQUEST_DOWNLOAD_FILE: number;
                AGENT_REQUEST_PREVIEW_FILE: number;
                AGENT_REQUEST_UPLOAD_RESOURCE: number;
                AGENT_REQUEST_UPLOAD_FILE: number;
                AGENT_REQUEST_UPLOAD_FILE_RESOURCE: number;
                AGENT_REQUEST_CREATE_FOLDER: number;
                AGENT_REQUEST_MOVE_ENTRY: number;
                AGENT_REQUEST_DELETE_ENTRY: number;
                AGENT_REQUEST_QUERY_UPLOAD_PROGRESS: number;
                AGENT_PUBLISH_RESPONSE: number;
                AGENT_SERVE_FILE: number;
                AGENT_REQUEST_ONLINE: number;
                AGENT_REQUEST_OFFLINE: number;
                AGENT_REQUEST_MAX: number;
                WEBAPP_REQUEST_CREATE: number;
                WEBAPP_REQUEST_READ: number;
                WEBAPP_REQUEST_UPDATE: number;
                WEBAPP_REQUEST_DELETE: number;
                WEBAPP_REQUEST_LIST: number;
                WEBAPP_REQUEST_LIST_BOT: number;
                WEBAPP_REQUEST_LIST_EMBEDDED: number;
                WEBAPP_REQUEST_LIST_SUBSCRIPTION: number;
                WEBAPP_REQUEST_LIST_INBOX_BOT: number;
                WEBAPP_REQUEST_DOWNLOAD_RESOURCE: number;
                WEBAPP_REQUEST_UPLOAD_RESOURCE: number;
                WEBAPP_REQUEST_UPLOAD_PICTURE: number;
                WEBAPP_REQUEST_CREATE_TOKEN: number;
                WEBAPP_REQUEST_MAX: number;
                GROUP_REQUEST_CREATE: number;
                GROUP_REQUEST_READ_CAPABILITY: number;
                GROUP_REQUEST_REGISTER: number;
                GROUP_REQUEST_LIST_AVAILABLE_BASE_DOMAINS: number;
                GROUP_REQUEST_CHECK_DOMAIN_AVAILABILITY: number;
                GROUP_REQUEST_UNREGISTER: number;
                GROUP_REQUEST_READ: number;
                GROUP_REQUEST_READ_MEMBERS: number;
                GROUP_REQUEST_READ_SORT_MEMBERS: number;
                GROUP_REQUEST_READ_MANAGEMENT_MEMBERS: number;
                GROUP_REQUEST_EXPORT_MEMBERS: number;
                GROUP_REQUEST_EXPORT_USER_ACTIVITIES: number;
                GROUP_REQUEST_EXPORT_CLIENT_ENGAGEMENT: number;
                GROUP_REQUEST_EXPORT_INTERNAL_USER_ENGAGEMENT: number;
                GROUP_REQUEST_EXPORT_CLIENT_COVERAGE: number;
                GROUP_REQUEST_EXPORT_SOCIAL_ENGAGEMENT: number;
                GROUP_REQUEST_READ_MANAGEMENT_USER_ACTIVITIES: number;
                GROUP_REQUEST_READ_MANAGEMENT_USER_BOARDS: number;
                GROUP_REQUEST_EXPORT_SERVICE_REQUEST_SUMMARY: number;
                GROUP_REQUEST_EXPORT_SERVICE_REQUEST_AGENT_SUMMARY: number;
                GROUP_REQUEST_EXPORT_ACD_SUMMARY: number;
                GROUP_REQUEST_EXPORT_ACD_AGENT_SUMMARY: number;
                GROUP_REQUEST_EXPORT_CLIENT_USAGE: number;
                GROUP_REQUEST_EXPORT_INTERNAL_USAGE: number;
                GROUP_REQUEST_EXPORT_DAILY_ORG_ACTIVITY: number;
                GROUP_REQUEST_EXPORT_DAILY_USER_ACTIVITY: number;
                GROUP_REQUEST_UPDATE: number;
                GROUP_REQUEST_UPDATE_ALIAS: number;
                GROUP_REQUEST_UPDATE_MEMBER_ALIAS: number;
                GROUP_REQUEST_READ_ALIAS: number;
                GROUP_REQUEST_CANCEL: number;
                GROUP_REQUEST_READ_USAGE: number;
                GROUP_REQUEST_SUBSCRIBE: number;
                GROUP_REQUEST_UNSUBSCRIBE: number;
                GROUP_REQUEST_READ_APP_ASSOCIATION: number;
                GROUP_REQUEST_READ_APP_ASSETLINKS: number;
                GROUP_REQUEST_READ_TELEPHONY_DOMAIN: number;
                GROUP_REQUEST_INVITE: number;
                GROUP_REQUEST_INVITE_CSV_IMPORT: number;
                GROUP_REQUEST_INVITE_CSV_IMPORT_BY_INTERNAL: number;
                GROUP_REQUEST_JOIN: number;
                GROUP_REQUEST_JOIN_VIA_INVITATION: number;
                GROUP_REQUEST_LEAVE: number;
                GROUP_REQUEST_RESEND_INVITATION_EMAIL: number;
                GROUP_REQUEST_INVITATION_CONFIRM_EMAIL: number;
                GROUP_REQUEST_RESEND_INVITATION_SMS: number;
                GROUP_REQUEST_INVITATION_CONFIRM_SMS: number;
                GROUP_REQUEST_EXPEL: number;
                GROUP_REQUEST_REMOVE_MEMBER: number;
                GROUP_REQUEST_SET_ACCESS_TYPE: number;
                GROUP_REQUEST_VIEW_INVITATION: number;
                GROUP_REQUEST_DOWNLOAD_RESOURCE: number;
                GROUP_REQUEST_UPLOAD_RESOURCE: number;
                GROUP_REQUEST_USER_READ: number;
                GROUP_REQUEST_USER_UPDATE: number;
                GROUP_REQUEST_USER_UPDATE_EMAIL: number;
                GROUP_REQUEST_USER_UPDATE_EMAIL_PHONE_NUMBER: number;
                GROUP_REQUEST_USER_DISABLE: number;
                GROUP_REQUEST_USER_ENABLE: number;
                GROUP_REQUEST_USER_TRANSFER: number;
                GROUP_REQUEST_USER_ARCHIVE: number;
                GROUP_REQUEST_USERS_READ: number;
                GROUP_REQUEST_READ_USER_BUSY_TIME: number;
                GROUP_REQUEST_IMPORT_REDEEM_URL: number;
                GROUP_REQUEST_RESET_REDEEM_URL: number;
                GROUP_REQUEST_GET_REDEEM_URL: number;
                GROUP_REQUEST_CREATE_BOARD_PROPERTY: number;
                GROUP_REQUEST_UPDATE_BOARD_PROPERTY: number;
                GROUP_REQUEST_DELETE_BOARD_PROPERTY: number;
                GROUP_REQUEST_BOARD_LEAVE: number;
                GROUP_REQUEST_BOARD_CREATE: number;
                GROUP_REQUEST_BOARD_DELETE: number;
                GROUP_REQUEST_BOARD_ADD_MEMBER: number;
                GROUP_REQUEST_BOARD_REMOVE_MEMBER: number;
                GROUP_REQUEST_BOARD_UPDATE_MEMBER: number;
                GROUP_REQUEST_BOARD_UPDATE: number;
                GROUP_REQUEST_SESSION_SCHEDULE: number;
                GROUP_REQUEST_CREATE_RELATION: number;
                GROUP_REQUEST_INVITE_AND_CREATE_RELATION: number;
                GROUP_REQUEST_CONFIRM_RELATION: number;
                GROUP_REQUEST_TRANSFER_RELATION: number;
                GROUP_REQUEST_DELETE_RELATION: number;
                GROUP_REQUEST_UPDATE_RELATION: number;
                GROUP_REQUEST_INVITE_BOARD_REQUESTING_USER: number;
                GROUP_REQUEST_CREATE_BOT_RELATION: number;
                GROUP_REQUEST_DELETE_BOT_RELATION: number;
                GROUP_REQUEST_CREATE_INTEGRATION: number;
                GROUP_REQUEST_UPDATE_INTEGRATION: number;
                GROUP_REQUEST_DELETE_INTEGRATION: number;
                GROUP_REQUEST_VERIFY_INTEGRATION: number;
                GROUP_REQUEST_GET_INTEGRATION_USER_ACCESSTOKEN: number;
                GROUP_REQUEST_READ_TASKS: number;
                GROUP_REQUEST_STRIPE_WEBHOOK: number;
                GROUP_REQUEST_STRIPE_CUSTOMER: number;
                GROUP_REQUEST_STRIPE_SUBSCRIBE: number;
                GROUP_REQUEST_STRIPE_PRICE: number;
                GROUP_REQUEST_STRIPE_INVOICES: number;
                GROUP_REQUEST_STRIPE_UPCOMING_INVOICE: number;
                GROUP_REQUEST_STRIPE_COUPON: number;
                GROUP_REQUEST_STRIPE_PUBLISHABLE_KEY: number;
                GROUP_REQUEST_CREATE_TEAM: number;
                GROUP_REQUEST_UPDATE_TEAM: number;
                GROUP_REQUEST_DELETE_TEAM: number;
                GROUP_REQUEST_CREATE_PUBLIC_TEAM: number;
                GROUP_REQUEST_UPDATE_PUBLIC_TEAM: number;
                GROUP_REQUEST_DELETE_PUBLIC_TEAM: number;
                GROUP_REQUEST_ADD_TEAM_MEMBER: number;
                GROUP_REQUEST_REMOVE_TEAM_MEMBER: number;
                GROUP_REQUEST_LEAVE_TEAM: number;
                GROUP_REQUEST_REASSIGN_TEAM_OWNER: number;
                GROUP_REQUEST_SET_TEAM_MEMBER_ACCESS_TYPE: number;
                GROUP_REQUEST_ADD_TEAM_MANAGER: number;
                GROUP_REQUEST_REMOVE_TEAM_MANAGER: number;
                GROUP_REQUEST_ASSIGN_TELEPHONY_DOMAIN: number;
                GROUP_REQUEST_ADD_INTEGRATION_SUBSCRIBER: number;
                GROUP_REQUEST_REMOVE_INTEGRATION_SUBSCRIBER: number;
                GROUP_REQUEST_READ_INTEGRATION_SUBSCRIBERS: number;
                GROUP_REQUEST_SET_BOARD_MEMBER_ACCESS_TYPE: number;
                GROUP_REQUEST_USER_READ_ACTIVITIES: number;
                GROUP_REQUEST_READ_GROUP_ACTIVITIES: number;
                GROUP_REQUEST_USER_POST_ACTIVITIES: number;
                GROUP_REQUEST_SUBSCRIBE_SERVICE_REQUESTS: number;
                GROUP_REQUEST_CREATE_ROLE: number;
                GROUP_REQUEST_UPDATE_ROLE: number;
                GROUP_REQUEST_DELETE_ROLE: number;
                GROUP_REQUEST_USER_RESET_PASSWORD: number;
                GROUP_REQUEST_USER_UPDATE_PICTURES: number;
                GROUP_REQUEST_USER_UPLOAD_PROFILE_PICTURES: number;
                GROUP_REQUEST_USER_UPLOAD_RESOURCE: number;
                GROUP_REQUEST_CREATE_SOCIAL_CONNECTION: number;
                GROUP_REQUEST_UPDATE_SOCIAL_CONNECTION: number;
                GROUP_REQUEST_DELETE_SOCIAL_CONNECTION: number;
                GROUP_REQUEST_READ_SOCIAL_CONNECTION: number;
                GROUP_REQUEST_JWT_TOKEN: number;
                GROUP_REQUEST_USER_RESET_PASSWORD_SMS: number;
                GROUP_REQUEST_CREATE_ROUTING_CHANNEL: number;
                GROUP_REQUEST_UPDATE_ROUTING_CHANNEL: number;
                GROUP_REQUEST_DELETE_ROUTING_CHANNEL: number;
                GROUP_REQUEST_UPDATE_USER_INVITATION_TOKEN: number;
                GROUP_REQUEST_READ_SUBSCRIPTION_BOARDS: number;
                GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARDS: number;
                GROUP_REQUEST_READ_CONTENT_LIBRARY_BOARD_COUNT: number;
                GROUP_REQUEST_UPDATE_CRM_REPORT: number;
                GROUP_REQUEST_READ_CRM_REPORT: number;
                GROUP_REQUEST_BOX_ACCESS_TOKEN: number;
                GROUP_REQUEST_SEARCH_USER_BOARD: number;
                GROUP_REQUEST_USER_BOARD_LOOKUP: number;
                GROUP_REQUEST_USER_UPDATE_OUT_OF_OFFICE: number;
                GROUP_REQUEST_INVITE_AND_USE_WORKFLOW_TEMPLATE: number;
                GROUP_REQUEST_USER_UPDATE_DISTRIBUTION_LIST: number;
                GROUP_REQUEST_USER_DELETE_FUTURE_SESSIONS: number;
                GROUP_REQUEST_USER_DELETE_SCHEDULE_BOARDS: number;
                GROUP_REQUEST_MAX: number;
                PRESENCE_REQUEST_READ: number;
                PRESENCE_USER_REQUEST_READ: number;
                PRESENCE_REQUEST_MESSAGE: number;
                PRESENCE_USER_REQUEST_UPDATE: number;
                PRESENCE_REQUEST_MAX: number;
                PARTNER_REQUEST_CREATE: number;
                PARTNER_REQUEST_READ: number;
                PARTNER_REQUEST_UPDATE: number;
                PARTNER_REQUEST_LIST: number;
                PARTNER_REQUEST_ADD_MEMBER: number;
                PARTNER_REQUEST_DELETE_MEMBER: number;
                PARTNER_REQUEST_VIEW_INVITATION: number;
                PARTNER_REQUEST_ADD_PLAN_CODE: number;
                PARTNER_REQUEST_DELETE_PLAN_CODE: number;
                PARTNER_REQUEST_CREATE_INTEGRATION: number;
                PARTNER_REQUEST_UPDATE_INTEGRATION: number;
                PARTNER_REQUEST_DELETE_INTEGRATION: number;
                PARTNER_REQUEST_READ_STATISTICS: number;
                PARTNER_REQUEST_READ_PLAN_CODES: number;
                PARTNER_REQUEST_READ_USAGE: number;
                PARTNER_REQUEST_CREATE_GROUP: number;
                PARTNER_REQUEST_LIST_GROUPS: number;
                PARTNER_REQUEST_CREATE_USERS: number;
                PARTNER_REQUEST_CREATE_TELEPHONY_DOMAIN: number;
                PARTNER_REQUEST_UPDATE_TELEPHONY_DOMAIN: number;
                PARTNER_REQUEST_DELETE_TELEPHONY_DOMAIN: number;
                PARTNER_REQUEST_LIST_TELEPHONY_DOMAINS: number;
                PARTNER_REQUEST_READ_TELEPHONY_DOMAIN: number;
                PARTNER_REQUEST_SET_DEFAULT_TELEPHONY_DOMAIN: number;
                PARTNER_REQUEST_CREATE_TELEPHONE_NUMBER: number;
                PARTNER_REQUEST_UPDATE_TELEPHONE_NUMBER: number;
                PARTNER_REQUEST_DELETE_TELEPHONE_NUMBER: number;
                PARTNER_REQUEST_UPLOAD_TELEPHONE_NUMBER_RESOURCE: number;
                PARTNER_REQUEST_DOWNLOAD_TELEPHONE_NUMBER_RESOURCE: number;
                PARTNER_REQUEST_MAX: number;
                TELEPHONY_REQUEST_ONCALLIN: number;
                TELEPHONY_REQUEST_SUBMIT_SESSIONKEY: number;
                TELEPHONY_REQUEST_SUBMIT_PARTICIPANTNUM: number;
                TELEPHONY_REQUEST_ONLEAVE: number;
                TELEPHONY_REQUEST_ON_SIPGATEWAY_CALL: number;
                TELEPHONY_REQUEST_POST_SIPGATEWAY_CALL: number;
                TELEPHONY_REQUEST_ON_TEXT_MESSAGE: number;
                TELEPHONY_REQUEST_ON_VOICE_MESSAGE: number;
                TELEPHONY_REQUEST_MAX: number;
                ROUTING_ACD_REQUEST_CREATE: number;
                ROUTING_ACD_REQUEST_UPDATE: number;
                ROUTING_ACD_REQUEST_ACCEPT: number;
                ROUTING_ACD_REQUEST_DECLINE: number;
                ROUTING_ACD_REQUEST_READ_OFFICE_HOUR: number;
                ROUTING_ACD_REQUEST_LEAVE_MESSAGE: number;
                ROUTING_ACD_REQUEST_ADD_BOT: number;
                ROUTING_ACD_REQUEST_REMOVE_BOT: number;
                ROUTING_SERVICE_REQUEST_SUBSCRIBE: number;
                ROUTING_SERVICE_REQUEST_LIST: number;
                ROUTING_SERVICE_REQUEST_CREATE: number;
                ROUTING_SERVICE_REQUEST_UPDATE: number;
                ROUTING_REQUEST_MAX: number;
                SEARCH_REQUEST_INDEX: number;
                SEARCH_REQUEST_SEARCH: number;
                SEARCH_REQUEST_MAX: number;
                SSO_SP_REQUEST_GET: number;
                SSO_SP_REQUEST_POST: number;
                SSO_SP_REQUEST_MAX: number;
                SSO_IDP_REQUEST_GET: number;
                SSO_IDP_REQUEST_POST: number;
                SSO_REQUEST_MAX: number;
                ACTIVITY_REQUEST_QUERY: number;
                ACTIVITY_REQUEST_MAX: number;
                CLIENT_USERS_FLOW_ACTION_SUMMARY: number;
                INT_USERS_FLOW_ACTION_SUMMARY: number;
                FLOW_ACTION_LIST: number;
                FLOW_BINDER_LIST: number;
                EXTERNAL_USERS: number;
                INTERNAL_USERS: number;
                WORKSPACES: number;
                WORKSPACE_ACTIONS: number;
                GROUP_REQUEST_EXPORT_WORKSPACE_LIST: number;
                GROUP_REQUEST_EXPORT_ACTION_LIST: number;
                USERS_WITH_OPEN_ACTIONS_CNT: number;
                GROUP_REQUEST_EXPORT_MEETING_USAGE: number;
                GROUP_REQUEST_EXPORT_MEETING_LIST: number;
                GROUP_REQUEST_EXPORT_SR_LIST: number;
            };
        };
        ClientResponseCode: {
            edition: string;
            values: {
                RESPONSE_CONNECT_SUCCESS: number;
                RESPONSE_ERROR_UPGRADE_REQUIRED: number;
                RESPONSE_SUCCESS: number;
                RESPONSE_ACCEPTED: number;
                RESPONSE_NO_CONTENT: number;
                RESPONSE_ERROR_STATUS_MOVED: number;
                RESPONSE_ERROR_X_ACCEL_REDIRECT: number;
                RESPONSE_ERROR_TEMPORARY_REDIRECTION: number;
                RESPONSE_ERROR_INVALID_REQUEST: number;
                RESPONSE_ERROR_INVALID_TOKEN: number;
                RESPONSE_ERROR_PAYMENT_REQUIRED: number;
                RESPONSE_ERROR_PERMISSION: number;
                RESPONSE_ERROR_NOT_FOUND: number;
                RESPONSE_ERROR_INVALID_OBJECT: number;
                RESPONSE_ERROR_TIMEOUT: number;
                RESPONSE_ERROR_CONFLICT: number;
                RESPONSE_ERROR_PRECONDITION_FAILED: number;
                RESPONSE_ERROR_EXCEED_LIMIT: number;
                RESPONSE_ERROR_TOO_MANY_REQUESTS: number;
                RESPONSE_ERROR_FAILED: number;
                RESPONSE_ERROR_BAD_GATEWAY: number;
                RESPONSE_ERROR_SERVICE_UNAVAILABLE: number;
                RESPONSE_SUBSCRIPTION_DATA: number;
                RESPONSE_CONNECTION_TOKEN_VERIFIED: number;
                RESPONSE_ERROR_DISCONNECTED: number;
            };
        };
        ClientResponseDetailCode: {
            edition: string;
            values: {
                DETAIL_CODE_NO_DETAILS: number;
                DETAIL_CODE_CLIENT_UPGRADE_RECOMMENDED: number;
                EXCEED_USER_BOARDS_MAX: number;
                EXCEED_BOARD_PAGES_MAX: number;
                EXCEED_BOARD_USERS_MAX: number;
                EXCEED_SESSION_USERS_MAX: number;
                EXCEED_GROUP_BOARDS_MAX: number;
                EXCEED_GROUP_USERS_MAX: number;
                EXCEED_UPLOAD_CLIENT_BODY_MAX: number;
                EXCEED_USER_CLOUD_MAX: number;
                EXCEED_NAME_LENGTH_MAX: number;
                ERROR_USER_DISABLED: number;
                ERROR_GROUP_SUBSCRIPTION_EXPIRED: number;
                ERROR_SSO_ENFORCED: number;
                ERROR_INVALID_BOARD_ID: number;
                ERROR_VIRUS_DETECTED: number;
                ERROR_FILE_TYPE_NOT_SUPPORTED: number;
                ERROR_PASSWORD_RULE_CONFLICT: number;
                ERROR_VERIFICATION_CODE_EXPIRED: number;
                ERROR_BOARD_VIEW_TOKEN_EXPIRED: number;
                ERROR_LOGIN_LOCKED: number;
                ERROR_USER_NOT_REGISTERED: number;
                ERROR_USER_NOT_GROUP_MEMBER: number;
                ERROR_USER_NOT_AUTHORIZED: number;
                ERROR_NOT_EMPTY: number;
                ERROR_USER_NOT_LOGIN: number;
                ERROR_INVALID_USER_TYPE: number;
                ERROR_2FA_REQUIRED: number;
                EXCEED_FILE_SIZE_MAX: number;
                ERROR_INVALID_FILE_ENCODING: number;
                EXCEED_FILE_LINES_MAX: number;
                ERROR_INVALID_FILE_FORMAT: number;
                ERROR_INVALID_FILE_HEADER: number;
                ERROR_INVALID_FIELD: number;
                ERROR_DUPLICATE_FIELD: number;
                ERROR_EXPECTED_FIELD: number;
                AGENT_ERROR_INVALID_PASSCODE: number;
                AGENT_ERROR_INVALID_TIMESTAMP: number;
                AGENT_ERROR_INVALID_PATH: number;
                ERROR_INTEGRATION_INVALID_GROUP: number;
                ERROR_INTEGRATION_EXPIRED_GROUP_SUBSCRIPTION: number;
                ERROR_INTEGRATION_INVALID_EXTERNAL_RESPONSE: number;
                ERROR_INTEGRATION_NOT_GROUP_MEMBER: number;
                ERROR_INTEGRATION_EXCEED_GROUP_MEMBER_QUANTITY: number;
                ERROR_INTEGRATION_PENDING_GROUP_MEMBER: number;
                ERROR_SESSION_NOT_STARTED: number;
                ERROR_SESSION_ENDED: number;
                ERROR_SESSION_PASSWORD_PROTECTED: number;
                ERROR_SESSION_WAITING_LIST_ENABLED: number;
                ERROR_SESSION_LOCKED: number;
                ERROR_FLOW_STEP_INVALID_STATUS: number;
            };
        };
        UserActivityRoutingDetail: {
            edition: string;
            fields: {
                board_id: {
                    type: string;
                    id: number;
                };
                duration: {
                    type: string;
                    id: number;
                };
                count: {
                    type: string;
                    id: number;
                };
            };
        };
        UserActivityLogDetail: {
            edition: string;
            fields: {
                object: {
                    type: string;
                    id: number;
                };
                routing_detail: {
                    type: string;
                    id: number;
                };
            };
        };
        UserActivityLogEntry: {
            edition: string;
            fields: {
                action_group_id: {
                    type: string;
                    id: number;
                };
                action_group: {
                    type: string;
                    id: number;
                };
                action_type_id: {
                    type: string;
                    id: number;
                };
                action_type: {
                    type: string;
                    id: number;
                };
                action_description: {
                    type: string;
                    id: number;
                };
                platform: {
                    type: string;
                    id: number;
                };
                browser: {
                    type: string;
                    id: number;
                };
                device_model: {
                    type: string;
                    id: number;
                };
                client_private_ip: {
                    type: string;
                    id: number;
                };
                client_public_ip: {
                    type: string;
                    id: number;
                };
                client_version: {
                    type: string;
                    id: number;
                };
                detail: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserEngagementQuery: {
            edition: string;
            fields: {
                actor_id: {
                    type: string;
                    id: number;
                };
                peer_actor_id: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        QueryFilter: {
            edition: string;
            fields: {
                key: {
                    type: string;
                    id: number;
                };
                value: {
                    type: string;
                    id: number;
                };
                op: {
                    type: string;
                    id: number;
                };
            };
        };
        PaginationFilter: {
            edition: string;
            fields: {
                conditions: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        UserActivityLog: {
            edition: string;
            fields: {
                actor_org_id: {
                    type: string;
                    id: number;
                };
                actor_id: {
                    type: string;
                    id: number;
                };
                actor_email: {
                    type: string;
                    id: number;
                };
                actor_unique_id: {
                    type: string;
                    id: number;
                };
                actor_phone_number: {
                    type: string;
                    id: number;
                };
                actor_type: {
                    type: string;
                    id: number;
                };
                actor: {
                    type: string;
                    id: number;
                };
                suppress_stats: {
                    type: string;
                    id: number;
                };
                activities: {
                    rule: string;
                    type: string;
                    id: number;
                };
                actor_ids: {
                    rule: string;
                    type: string;
                    id: number;
                };
                from_date: {
                    type: string;
                    id: number;
                };
                to_date: {
                    type: string;
                    id: number;
                };
                action_group_id: {
                    rule: string;
                    type: string;
                    id: number;
                };
                action_type_id: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_engagement_query: {
                    rule: string;
                    type: string;
                    id: number;
                };
                page_start: {
                    type: string;
                    id: number;
                };
                page_size: {
                    type: string;
                    id: number;
                };
                page_number: {
                    type: string;
                    id: number;
                };
                sort_field_name: {
                    type: string;
                    id: number;
                };
                sort_method: {
                    type: string;
                    id: number;
                };
                filters: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        Engagement: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                active_client_count: {
                    type: string;
                    id: number;
                };
                binder_active_client_count: {
                    type: string;
                    id: number;
                };
                p2p_active_client_count: {
                    type: string;
                    id: number;
                };
                group_active_client_count: {
                    type: string;
                    id: number;
                };
                session_count: {
                    type: string;
                    id: number;
                };
                doc_shared_count: {
                    type: string;
                    id: number;
                };
                chat_count: {
                    type: string;
                    id: number;
                };
                meet_count: {
                    type: string;
                    id: number;
                };
                anotation_count: {
                    type: string;
                    id: number;
                };
            };
        };
        SocialEngagement: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                social_binder_create_count: {
                    type: string;
                    id: number;
                };
                social_binder_active_count: {
                    type: string;
                    id: number;
                };
                client_chat_count: {
                    type: string;
                    id: number;
                };
                internal_chat_count: {
                    type: string;
                    id: number;
                };
                client_doc_shared_count: {
                    type: string;
                    id: number;
                };
                internal_doc_shared_count: {
                    type: string;
                    id: number;
                };
                unboard_client_count: {
                    type: string;
                    id: number;
                };
                unreply_message_count: {
                    type: string;
                    id: number;
                };
            };
        };
        SocialEngagementRecord: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                wechat_binder_create_count: {
                    type: string;
                    id: number;
                };
                wechat_binder_active_count: {
                    type: string;
                    id: number;
                };
                wechat_client_chat_count: {
                    type: string;
                    id: number;
                };
                wechat_internal_chat_count: {
                    type: string;
                    id: number;
                };
                wechat_client_doc_shared_count: {
                    type: string;
                    id: number;
                };
                wechat_internal_doc_shared_count: {
                    type: string;
                    id: number;
                };
                whatsapp_binder_create_count: {
                    type: string;
                    id: number;
                };
                whatsapp_binder_active_count: {
                    type: string;
                    id: number;
                };
                whatsapp_client_chat_count: {
                    type: string;
                    id: number;
                };
                whatsapp_internal_chat_count: {
                    type: string;
                    id: number;
                };
                whatsapp_client_doc_shared_count: {
                    type: string;
                    id: number;
                };
                whatsapp_internal_doc_shared_count: {
                    type: string;
                    id: number;
                };
            };
        };
        ClientCoverage: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                };
                client_count: {
                    type: string;
                    id: number;
                };
                engaged_client_count: {
                    type: string;
                    id: number;
                };
                total_engaged_client_count: {
                    type: string;
                    id: number;
                };
                doc_shared_count: {
                    type: string;
                    id: number;
                };
                chat_count: {
                    type: string;
                    id: number;
                };
                meet_count: {
                    type: string;
                    id: number;
                };
                left_client_count: {
                    type: string;
                    id: number;
                };
                assigned_client_count: {
                    type: string;
                    id: number;
                };
                client_coverage: {
                    type: string;
                    id: number;
                };
                client_daily_coverage: {
                    type: string;
                    id: number;
                };
            };
        };
        UserEngageMent: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                };
                peer_user: {
                    type: string;
                    id: number;
                };
                msg_count: {
                    type: string;
                    id: number;
                };
                peer_msg_count: {
                    type: string;
                    id: number;
                };
                doc_shared_count: {
                    type: string;
                    id: number;
                };
                peer_doc_shared_count: {
                    type: string;
                    id: number;
                };
                meet_count: {
                    type: string;
                    id: number;
                };
                esign_count: {
                    type: string;
                    id: number;
                };
                peer_esign_count: {
                    type: string;
                    id: number;
                };
                active_relation_total: {
                    type: string;
                    id: number;
                };
                todo_count: {
                    type: string;
                    id: number;
                };
                peer_todo_count: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
            };
        };
        ACDSummary: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                new_acd_number: {
                    type: string;
                    id: number;
                };
                accepted_acd_number: {
                    type: string;
                    id: number;
                };
                request_timeout_acd_number: {
                    type: string;
                    id: number;
                };
                leave_msg_acd_number: {
                    type: string;
                    id: number;
                };
                total_wait_time: {
                    type: string;
                    id: number;
                };
                has_guest_number: {
                    type: string;
                    id: number;
                };
                has_meet_number: {
                    type: string;
                    id: number;
                };
                has_file_number: {
                    type: string;
                    id: number;
                };
                total_msg_file_number: {
                    type: string;
                    id: number;
                };
                total_acd_duration: {
                    type: string;
                    id: number;
                };
                new_anonymous_acd_number: {
                    type: string;
                    id: number;
                };
                closed_acd_number: {
                    type: string;
                    id: number;
                };
            };
        };
        ACDAgentSummary: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                };
                received_acd_number: {
                    type: string;
                    id: number;
                };
                accepted_acd_number: {
                    type: string;
                    id: number;
                };
                rejected_acd_number: {
                    type: string;
                    id: number;
                };
                missed_acd_number: {
                    type: string;
                    id: number;
                };
                has_guest_number: {
                    type: string;
                    id: number;
                };
                has_meet_number: {
                    type: string;
                    id: number;
                };
                has_file_number: {
                    type: string;
                    id: number;
                };
                total_msg_file_number: {
                    type: string;
                    id: number;
                };
                total_acd_duration: {
                    type: string;
                    id: number;
                };
                closed_acd_number: {
                    type: string;
                    id: number;
                };
            };
        };
        SRSummary: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                new_sr_number: {
                    type: string;
                    id: number;
                };
                new_unassigned_number: {
                    type: string;
                    id: number;
                };
                return_to_inbox_number: {
                    type: string;
                    id: number;
                };
                assigned_number: {
                    type: string;
                    id: number;
                };
                assigned_no_agent_response_number: {
                    type: string;
                    id: number;
                };
                total_assignment_time: {
                    type: string;
                    id: number;
                };
                total_resolution_time: {
                    type: string;
                    id: number;
                };
                active_sr_number: {
                    type: string;
                    id: number;
                };
                total_msg_file_number: {
                    type: string;
                    id: number;
                };
                agent_resolve_number: {
                    type: string;
                    id: number;
                };
                client_close_number: {
                    type: string;
                    id: number;
                };
                client_reopen_number: {
                    type: string;
                    id: number;
                };
                reassigned_number: {
                    type: string;
                    id: number;
                };
                unique_client_number: {
                    type: string;
                    id: number;
                };
            };
        };
        SRAgentSummary: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                };
                assigned_to_other_number: {
                    type: string;
                    id: number;
                };
                assigned_to_self_number: {
                    type: string;
                    id: number;
                };
                total_assignment_time: {
                    type: string;
                    id: number;
                };
                assigned_no_response_number: {
                    type: string;
                    id: number;
                };
                return_to_inbox_number: {
                    type: string;
                    id: number;
                };
                resolve_by_self_number: {
                    type: string;
                    id: number;
                };
                close_by_client_number: {
                    type: string;
                    id: number;
                };
                total_msg_file_number: {
                    type: string;
                    id: number;
                };
                total_resolution_time: {
                    type: string;
                    id: number;
                };
                active_sr_number: {
                    type: string;
                    id: number;
                };
                client_reopen_number: {
                    type: string;
                    id: number;
                };
                assigned_to_self_number_new: {
                    type: string;
                    id: number;
                };
                reassigned_number: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupReport: {
            edition: string;
            fields: {
                members: {
                    rule: string;
                    type: string;
                    id: number;
                };
                total_number: {
                    type: string;
                    id: number;
                };
            };
        };
        MoxoReport: {
            edition: string;
            fields: {
                client_engagement: {
                    rule: string;
                    type: string;
                    id: number;
                };
                internal_user_engagement: {
                    rule: string;
                    type: string;
                    id: number;
                };
                client_coverage: {
                    rule: string;
                    type: string;
                    id: number;
                };
                social_engagement: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_engagement: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_activity_summary: {
                    rule: string;
                    type: string;
                    id: number;
                };
                client_changes: {
                    rule: string;
                    type: string;
                    id: number;
                };
                acd_summary: {
                    rule: string;
                    type: string;
                    id: number;
                };
                acd_agent_summary: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sr_summary: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sr_agent_summary: {
                    rule: string;
                    type: string;
                    id: number;
                };
                group_report: {
                    type: string;
                    id: number;
                };
            };
        };
        ClientRequests: {
            edition: string;
            fields: {
                requests: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        ClientRequest: {
            edition: string;
            fields: {
                type: {
                    type: string;
                    id: number;
                };
                original_type: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                params: {
                    rule: string;
                    type: string;
                    id: number;
                };
                object: {
                    type: string;
                    id: number;
                };
                request_body: {
                    type: string;
                    id: number;
                };
                request_body_file_path: {
                    type: string;
                    id: number;
                };
                request_body_content_type: {
                    type: string;
                    id: number;
                };
                message: {
                    type: string;
                    id: number;
                };
                user_activity: {
                    type: string;
                    id: number;
                };
                note: {
                    type: string;
                    id: number;
                };
            };
        };
        ClientResponse: {
            edition: string;
            fields: {
                code: {
                    type: string;
                    id: number;
                };
                detail_code: {
                    type: string;
                    id: number;
                };
                redirect_url: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                message: {
                    type: string;
                    id: number;
                };
                data: {
                    type: string;
                    id: number;
                };
                expires_in: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                server: {
                    type: string;
                    id: number;
                };
                session_id: {
                    type: string;
                    id: number;
                };
                request_sequence: {
                    type: string;
                    id: number;
                };
                connection_id: {
                    type: string;
                    id: number;
                };
                zone: {
                    type: string;
                    id: number;
                };
                domain: {
                    type: string;
                    id: number;
                };
                is_truncated: {
                    type: string;
                    id: number;
                };
                marker: {
                    type: string;
                    id: number;
                };
                next_marker: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                object: {
                    type: string;
                    id: number;
                };
                headers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                params: {
                    rule: string;
                    type: string;
                    id: number;
                };
                hits: {
                    type: string;
                    id: number;
                };
                start: {
                    type: string;
                    id: number;
                };
                size: {
                    type: string;
                    id: number;
                };
                recordings: {
                    rule: string;
                    type: string;
                    id: number;
                };
                audios: {
                    rule: string;
                    type: string;
                    id: number;
                };
                videos: {
                    rule: string;
                    type: string;
                    id: number;
                };
                dss: {
                    rule: string;
                    type: string;
                    id: number;
                };
                token: {
                    type: string;
                    id: number;
                };
                client_message: {
                    type: string;
                    id: number;
                };
                client_messages: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_activities: {
                    rule: string;
                    type: string;
                    id: number;
                };
                report: {
                    type: string;
                    id: number;
                };
                group_capability: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupCapability: {
            edition: string;
            fields: {
                has_sms_config: {
                    type: string;
                    id: number;
                };
            };
        };
        ClientRequestParameter: {
            edition: string;
            options: {
                allow_alias: boolean;
            };
            values: {
                USER_REQUEST_REGISTER_NO_QS_BOARDS: number;
                USER_REQUEST_SALES_FORCE_CONNECT_URL: number;
                USER_REQUEST_READ_SET_COOKIE: number;
                USER_REQUEST_GET_ACCESS_TOKEN: number;
                USER_REQUEST_READ_TIMESTAMP: number;
                BOARD_REQUEST_READ_TIMESTAMP: number;
                SERVER_OBJECT_READ_TIMESTAMP: number;
                GROUP_REQUEST_READ_USAGE_TIMESTAMP: number;
                GROUP_REQUEST_READ_TASKS_TIMESTAMP: number;
                USER_REQUEST_READ_COUNT: number;
                BOARD_REQUEST_READ_COUNT: number;
                USER_REQUEST_READ_TIMESTAMP_FROM: number;
                GROUP_REQUEST_READ_TIMESTAMP_FROM: number;
                USER_REQUEST_READ_TIMESTAMP_TO: number;
                GROUP_REQUEST_READ_TIMESTAMP_TO: number;
                USER_REQUEST_READ_TIMESTAMP_OFFSET: number;
                GROUP_REQUEST_READ_TIMESTAMP_OFFSET: number;
                GROUP_REQUEST_EXPORT_REPORT_HEADERS: number;
                GROUP_REQUEST_EXPORT_DATE_LOCALE: number;
                USER_REQUEST_REPORT_RUN_BY: number;
                OUTPUT_FILTER_STRING: number;
                LOGIN_OUTPUT_USER_FILTER_STRING: number;
                USER_REQUEST_LOGIN_REMEMBER: number;
                USER_REQUEST_LOGIN_OUTPUT_BASIC: number;
                USER_REQUEST_LOGIN_LOCAL_USER_EXPECTED: number;
                GROUP_REQUEST_LOCAL_USER_EXPECTED: number;
                USER_REQUEST_LOGIN_PARTNER_ADMIN_EXPECTED: number;
                USER_REQUEST_LOGOUT_KEEP_DEVICE_TOKEN: number;
                USER_REQUEST_REMEMBER_DEVICE: number;
                USER_REQUEST_RESET_PASSWORD_PARTNER_ADMIN_EXPECTED: number;
                USER_REQUEST_PASSWORD_ENCODED: number;
                USER_REQUEST_KEEP_TOKEN: number;
                USER_REQUEST_ALL_BOARDS: number;
                USER_REQUEST_OLD_BOARDS: number;
                USER_REQUEST_FEEDBACK_MESSAGE: number;
                USER_REQUEST_FEEDBACK_SUBJECT: number;
                USER_SUBSCRIBE_FILTER_MEET: number;
                USER_REQUEST_FILTER_ACD: number;
                USER_REQUEST_FILTER_SERVICE_REQUEST: number;
                USER_REQUEST_SSO_REDIRECT_URL: number;
                USER_REQUEST_CODE_TO_REGISTER: number;
                USER_REQUEST_CODE_TO_RESET_PASSWORD: number;
                USER_REQUEST_HTML_URL: number;
                USER_REQUEST_EMAIL_CODE: number;
                USER_REQUEST_SMS_CODE: number;
                USER_REQUEST_APPLE_JWT: number;
                USER_REQUEST_GOOGLE_JWT: number;
                USER_REQUEST_DEVICE_TOKEN_VENDOR: number;
                USER_REQUEST_DEVICE_TOKEN_VENDOR_EXT: number;
                USER_REQUEST_CONTACT_INVITE_TOKEN: number;
                GROUP_REQUEST_INVITE_MESSAGE: number;
                GROUP_REQUEST_INVITE_TOKEN: number;
                GROUP_REQUEST_USER_TOKEN: number;
                PARTNER_REQUEST_INVITE_TOKEN: number;
                BOARD_REQUEST_EXTERNAL_ID: number;
                BOARD_REQUEST_CREATE_AS_TEMP: number;
                BOARD_REQUEST_CREATE_AS_DEFAULT: number;
                BOARD_REQUEST_CLEAR_VIEW_TOKEN: number;
                BOARD_REQUEST_NEW_BOARD: number;
                BOARD_REQUEST_CREATE_AS_SUBSCRIPTION_CHANNEL: number;
                BOARD_REQUEST_CREATE_SIGNATURE_AS_TEMPLATE: number;
                BOARD_REQUEST_SUBMIT_SIGNATURE_KEEP_STATUS_UNCHANGED: number;
                BOARD_REQUEST_READ_UPLOAD_SEQUENCE: number;
                BOARD_REQUEST_VIEW_TOKEN: number;
                SESSION_REQUEST_ROSTER_TOKEN: number;
                BOARD_REQUEST_SEARCH_TEXT: number;
                BOARD_REQUEST_SEARCH_START: number;
                BOARD_REQUEST_SEARCH_SIZE: number;
                BOARD_REQUEST_SEARCH_CREATOR: number;
                BOARD_REQUEST_SEARCH_ID: number;
                BOARD_REQUEST_SEARCH_BOARD_TYPE: number;
                BOARD_REQUEST_KEEP_UNREAD_FEED_TIMESTAMP: number;
                GROUP_REQUEST_SEARCH_TEXT: number;
                GROUP_REQUEST_SEARCH_START: number;
                GROUP_REQUEST_SEARCH_SIZE: number;
                GROUP_REQUEST_SEARCH_PAGE_NUMBER: number;
                GROUP_REQUEST_SEARCH_SORT_FIELD: number;
                GROUP_REQUEST_SEARCH_SORT_METHOD: number;
                PARTNER_REQUEST_SEARCH_TEXT: number;
                PARTNER_REQUEST_SEARCH_START: number;
                PARTNER_REQUEST_SEARCH_SIZE: number;
                GROUP_REQUEST_ACTION_GROUP: number;
                GROUP_REQUEST_ACTION_TYPE: number;
                GROUP_REQUEST_SEARCH_MEMBER: number;
                GROUP_REQUEST_SUPPRESS_STATISTICS: number;
                USER_REQUEST_SEARCH_TEXT: number;
                USER_REQUEST_SEARCH_START: number;
                USER_REQUEST_SEARCH_SIZE: number;
                USER_REQUEST_SEARCH_PAGE_NUMBER: number;
                USER_REQUEST_SEARCH_CREATOR: number;
                USER_REQUEST_SEARCH_ID: number;
                USER_REQUEST_SEARCH_SORT_BY_TIME: number;
                USER_REQUEST_SEARCH_DUE_FROM: number;
                USER_REQUEST_SEARCH_DUE_TO: number;
                USER_REQUEST_SEARCH_EXCLUDE_CREATOR: number;
                USER_REQUEST_SEARCH_CREATED_OR_ASSIGNED: number;
                USER_REQUEST_SEARCH_CREATED_OR_SUBMITTED: number;
                USER_REQUEST_SEARCH_INCLUDE_CANCELED: number;
                USER_REQUEST_SEARCH_TIMELINE: number;
                USER_REQUEST_SEARCH_ARCHIVED: number;
                USER_REQUEST_SEARCH_INCLUDE_EDITING: number;
                BOARD_REQUEST_READ_FEEDS_INDEXED: number;
                BOARD_REQUEST_READ_FEEDS_ORIGINAL: number;
                BOARD_REQUEST_READ_WITH_DETAIL: number;
                BOARD_REQUEST_READ_WITHOUT_MEMBERS: number;
                GROUP_REQUEST_READ_GROUP_MEMBER: number;
                GROUP_REQEUEST_READ_MEMBER_PRESENCE: number;
                BOARD_REQUEST_COPY_PAGES_WITH_COMMENTS: number;
                BOARD_REQUEST_COPY_PAGES_WITHOUT_ANNOTATIONS: number;
                BOARD_REQUEST_COPY_TODOS_WITH_COMMENTS: number;
                BOARD_REQUEST_COPY_SIGNATURE_KEEP_CREATOR: number;
                BOARD_REQUEST_COPY_TRANSACTION_KEEP_CREATOR: number;
                BOARD_REQUEST_COPY_WITH_TOTAL_USED_COUNT: number;
                BOARD_REQUEST_COPY_WITH_LAST_MODIFIED_TIME: number;
                BOARD_REQUEST_DUPLICATE_WITH_SIGNATURES: number;
                BOARD_REQUEST_DUPLICATE_WITH_TRANSACTIONS: number;
                RESOURCE_REQUEST_RESOURCE_TYPE: number;
                RESOURCE_REQUEST_RESOURCE_ROTATION: number;
                RESOURCE_REQUEST_RESOURCE_WIDTH: number;
                RESOURCE_REQUEST_RESOURCE_HEIGHT: number;
                BOARD_REQUEST_RELATIVE_ORDER_NUMBER: number;
                RESOURCE_UPLOAD_RESOURCE_MEDIA_LENGTH: number;
                RESOURCE_UPLOAD_RESOURCE_DESCRIPTION: number;
                RESOURCE_DOWNLOAD_RESOURCE_CONTENT_DISPOSITION: number;
                RESOURCE_UPLOAD_RESOURCE_URL: number;
                RESOURCE_UPLOAD_RESOURCE_AUTHORIZATION: number;
                RESOURCE_UPLOAD_RESOURCE_DESTINATION_FOLDER: number;
                RESOURCE_UPLOAD_RESOURCE_CONTENT_TYPE: number;
                BOARD_REQUEST_INVITEE_EMAIL: number;
                BOARD_REQUEST_INVITE_MESSAGE: number;
                BOARD_REQUEST_PUSH_NOTIFICATION_MESSAGE: number;
                BOARD_REQUEST_PUSH_NOTIFICATION_OFF: number;
                GROUP_REQUEST_PUSH_NOTIFICATION_OFF: number;
                BOARD_REQUEST_EMAIL_OFF: number;
                USER_REQUEST_EMAIL_OFF: number;
                GROUP_REQUEST_EMAIL_OFF: number;
                BOARD_REQUEST_SMS_OFF: number;
                GROUP_REQUEST_SMS_OFF: number;
                BOARD_REQUEST_INVITE_ADD_DIRECTLY: number;
                BOARD_REQUEST_SIGNEE_MESSAGE: number;
                GROUP_REQUEST_RESEND_EMAIL: number;
                GROUP_REQUEST_RESEND_SMS: number;
                BOARD_REQUEST_INVITE_WITH_INFO_FROM: number;
                BOARD_REQUEST_DELETE_FOLDER_RECURSIVELY: number;
                BOARD_REQUEST_UPDATE_FILE_COVER: number;
                BOARD_REQUEST_CREATE_NEW_FILE: number;
                BOARD_REQUEST_NEW_FILE_NAME_SEQUENCE: number;
                BOARD_REQUEST_NEW_FILE_NAME: number;
                BOARD_REQUEST_ACTOR_NAME: number;
                BOARD_REQUEST_ACTOR_PICTURE_URL: number;
                BOARD_REQUEST_CUSTOM_INFO: number;
                BOARD_REQUEST_CUSTOM_INFO_SEQUENCE: number;
                BOARD_REQUEST_ORDER_NUMBER_SEQUENCE: number;
                BOARD_REQUEST_SOCIAL_CUSTOM_INFO: number;
                BOARD_REQUEST_MEMBER_VIEW_TOKEN: number;
                BOARD_REQUEST_RESET_MEMBER_STATUS: number;
                BOARD_REQUEST_AUTO_COMPLETE_TRANSACTION: number;
                BOARD_REQUEST_VIEW_TOKEN_TO_UPDATE_BOARD: number;
                BOARD_REQUEST_DUPLICATE_WITH_WORKFLOW: number;
                BOARD_REQUEST_DUPLICATE_AS_TEMPLATE: number;
                BOARD_REQUEST_CREATE_FLOW_VARIABLE: number;
                BOARD_REQUEST_NOT_FILTER_FLOW_VARIABLE: number;
                BOARD_REQUEST_COPY_KEEP_FLOW_STEP_UUID: number;
                BOARD_REQUEST_COPY_BOARD_THUMBNAIL: number;
                BOARD_REQUEST_COPY_BOARD_BANNER: number;
                BOARD_REQUEST_COPY_SKIP_ASSIGNEE_CHECKING: number;
                BOARD_REQUEST_COPY_WITH_COMPLETED_TODOS: number;
                BOARD_REQUEST_DUPLICATE_MILESTONE_FROM: number;
                BOARD_REQUEST_DUPLICATE_MILESTONE_TO: number;
                BOARD_REQUEST_COPY_BOARD_PROPERTY: number;
                BOARD_REQUEST_UPDATE_WORKFLOW_WITHOUT_LAST_MODIFIED_TIME: number;
                BOARD_REQUEST_REPLACE_TRANSACTION_WITH_STEPS: number;
                BOARD_REQUEST_COPY_WITH_WORKFLOW: number;
                SESSION_REQUEST_INVITE_MESSAGE: number;
                SESSION_REQUEST_RECORDING: number;
                SESSION_REQUEST_JOIN_INVISIBLE: number;
                GRAB_PRESENTER_WHEN_NOT_SHARING: number;
                SESSION_REQUEST_SESSION_KEY: number;
                BOARD_REQUEST_SUPPRESS_FEED: number;
                BOARD_REQUEST_SUPPRESS_JOB: number;
                BOARD_REQUEST_SUPPRESS_USER_ACTIVITY: number;
                SUBSCRIBE_REQUEST_NOHANG: number;
                BOARD_REQUEST_DOWNLOAD_BOARD_NO_WAIT: number;
                SUBSCRIBE_REQUEST_NO_GROUP_MEMBERS: number;
                SUBSCRIBE_REQUEST_NO_USER_BOARDS: number;
                SUBSCRIBE_REQUEST_RESPONSE_FILTER_OFF: number;
                CLIENT_CONNECTION_PUSH_NOTIFICATION_OFF: number;
                CLIENT_CONNECTION_SUBSCRIPTION_DATA_OFF: number;
                BOARD_REQUEST_FILE_REPLY: number;
                BOARD_REQUEST_SET_LAST_MODIFIED_TIME: number;
                BOARD_REQUEST_REASSIGN: number;
                BOARD_REQUEST_REASSIGN_FROM_GROUP_ID: number;
                BOARD_REQUEST_REASSIGN_TO_GROUP_ID: number;
                GROUP_REQUEST_READ_MEMBER_INTERNAL: number;
                GROUP_REQUEST_READ_MEMBER_LOCAL: number;
                GROUP_REQUEST_READ_GROUP_INVITED: number;
                GROUP_REQUEST_READ_GROUP_ADMIN: number;
                GROUP_REQUEST_READ_FILTER_ROLE: number;
                GROUP_REQUEST_READ_INCLUDE_RELATION_USER: number;
                GROUP_REQUEST_READ_INCLUDE_SUGGESTED_USER: number;
                GROUP_REQUEST_READ_DEACTIVED_USER: number;
                GROUP_REQUEST_READ_DELETED_USER: number;
                GROUP_REQUEST_READ_MEMBER_OUTPUT_COUNT: number;
                GROUP_REQUEST_READ_MEMBER_OUTPUT_CLIENT_TEAM: number;
                GROUP_REQUEST_READ_MEMBER_OUTPUT_TEAM: number;
                GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_INTERNAL: number;
                GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_LOCAL: number;
                GROUP_REQUEST_OUTPUT_USER_ACTIVITIES: number;
                GROUP_REQUEST_READ_MANAGEMENT_TEAM_MEMBERS: number;
                GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_REPORT: number;
                GROUP_REQUEST_READ_MANAGEMENT_MEMBERS_WITH_CLIENT_TEAMS: number;
                GROUP_REQUEST_READ_MEMBER_FOR_AUDIT: number;
                GROUP_REQUEST_EXPORT_REPORT_FORMATTED_RUN_ON: number;
                GROUP_REQUEST_EXPORT_REPORT_FORMATTED_FROM: number;
                GROUP_REQUEST_EXPORT_REPORT_FORMATTED_TO: number;
                USER_REQUEST_SEARCH_FILTER_MIN: number;
                USER_REQUEST_SEARCH_BOARD_NAME: number;
                USER_REQUEST_SEARCH_BOARD_USER: number;
                USER_REQUEST_SEARCH_BOARD_COMMENT: number;
                USER_REQUEST_SEARCH_BOARD_FILE: number;
                USER_REQUEST_SEARCH_BOARD_FOLDER: number;
                USER_REQUEST_SEARCH_BOARD_PAGE: number;
                USER_REQUEST_SEARCH_BOARD_PAGE_COMMENT: number;
                USER_REQUEST_SEARCH_BOARD_TODO: number;
                USER_REQUEST_SEARCH_BOARD_TODO_COMMENT: number;
                USER_REQUEST_SEARCH_BOARD_SESSION: number;
                USER_REQUEST_SEARCH_BOARD_SIGNATURE: number;
                USER_REQUEST_SEARCH_BOARD_SIGNATURE_PAGE: number;
                USER_REQUEST_SEARCH_BOARD_TRANSACTION: number;
                USER_REQUEST_SEARCH_BOARD_MENTION_COMMENT: number;
                USER_REQUEST_SEARCH_BOARD_APPROVAL: number;
                USER_REQUEST_SEARCH_BOARD_ACKNOWLEDGE: number;
                USER_REQUEST_SEARCH_BOARD_FILE_REQUEST: number;
                USER_REQUEST_SEARCH_BOARD_MEET_REQUEST: number;
                USER_REQUEST_SEARCH_BOARD_FORM_REQUEST: number;
                USER_REQUEST_SEARCH_BOARD_DOCUSIGN: number;
                USER_REQUEST_SEARCH_BOARD_WEBHOOK: number;
                USER_REQUEST_SEARCH_BOARD_LAUNCH_WEB_APP: number;
                USER_REQUEST_SEARCH_BOARD_INTEGRATION: number;
                USER_REQUEST_SEARCH_BOARD_TODO_TRANSACTION: number;
                USER_REQUEST_SEARCH_BOARD_WORKFLOW: number;
                USER_REQUEST_SEARCH_BOARD_DECISION: number;
                USER_REQUEST_SEARCH_BOARD_AWAIT: number;
                USER_REQUEST_SEARCH_BOARD_PDF_FORM: number;
                USER_REQUEST_SEARCH_FILTER_MAX: number;
                SERVER_REQUEST_INDEX_USER_INDEX: number;
                SERVER_REQUEST_INDEX_ORG_INDEX: number;
                SERVER_REQUEST_INDEX_FILTER: number;
                SERVER_REQUEST_INDEX_BOARD_NAME: number;
                SERVER_REQUEST_INDEX_BOARD_USER: number;
                SERVER_REQUEST_INDEX_BOARD_COMMENT: number;
                SERVER_REQUEST_INDEX_BOARD_FILE: number;
                SERVER_REQUEST_INDEX_BOARD_FOLDER: number;
                SERVER_REQUEST_INDEX_BOARD_PAGE: number;
                SERVER_REQUEST_INDEX_BOARD_PAGE_COMMENT: number;
                SERVER_REQUEST_INDEX_BOARD_TODO: number;
                SERVER_REQUEST_INDEX_BOARD_TODO_COMMENT: number;
                SERVER_REQUEST_INDEX_BOARD_SESSION: number;
                SERVER_REQUEST_INDEX_BOARD_SIGNATURE: number;
                SERVER_REQUEST_INDEX_BOARD_SIGNATURE_PAGE: number;
                SERVER_REQUEST_INDEX_BOARD_TRANSACTION: number;
                SERVER_REQUEST_INDEX_BOARD_ACTIONITEM: number;
                SERVER_REQUEST_INDEX_GLOBAL_USER_ORG_MAPPING: number;
                USER_REQUEST_READ_START: number;
                USER_REQUEST_READ_SIZE: number;
                USER_REQUEST_READ_PAGE_NUMBER: number;
                USER_REQUEST_READ_BEFORE: number;
                USER_REQUEST_READ_AFTER: number;
                USER_REQUEST_READ_TIMELINE: number;
                USER_REQUEST_READ_SR_OPEN: number;
                USER_REQUEST_READ_SR_COMPLETE: number;
                USER_REQUEST_READ_ARCHIVED: number;
                USER_REQUEST_READ_SUBSCRIPTION_CHANNEL: number;
                USER_REQUEST_READ_INBOX: number;
                USER_REQUEST_LOOKUP_P2P_BOARD: number;
                USER_REQUEST_READ_UNREAD_BOARD: number;
                USER_REQUEST_READ_CONVERSATION_BOARD: number;
                USER_REQUEST_READ_PROJECT_BOARD: number;
                USER_REQUEST_READ_SOCIAL_BOARD: number;
                USER_REQUEST_READ_SCHEDULE_BOARD: number;
                USER_REQUEST_USER_AGENT_EXT: number;
                BOARD_REQUEST_READ_SIZE_BEFORE: number;
                BOARD_REQUEST_READ_SIZE_AFTER: number;
                BOARD_REQUEST_READ_FIRST_SUB_FOLDER_FILE: number;
                BOARD_REQUEST_READ_POSITION_COMMENT_FEED: number;
                BOARD_REQUEST_EDITOR_TYPE_INTERNAL_ONLY: number;
                BOARD_REQUEST_REOPEN: number;
                BOARD_REQUEST_UPDATE_FEED: number;
                BOARD_REQUEST_KEEP_CURRENT_STATUS: number;
                BOARD_REQUEST_REOPEN_FEED: number;
                BOARD_REQUEST_REOPEN_EVENT: number;
                BOARD_REQUEST_READY_FEED: number;
                BOARD_REQUEST_RESET_REVIEWER_STATUS: number;
                BOARD_REQUEST_CREATE_FEED: number;
                BOARD_REQUEST_CREATE_NO_OWNER: number;
                BOARD_REQUEST_START_FLOW_WITHOUT_OWNER: number;
                BOARD_REQUEST_RESET_CODE: number;
                BOARD_REQUEST_VIEW_TOKEN_CODE: number;
                BOARD_REQUEST_MEET_REQUEST: number;
                BOARD_REQUEST_SET_RSVP_ACCEPTED: number;
                BOARD_REQUEST_CREATE_ATTACHMENT_FOLDER: number;
                BOARD_REQUEST_SET_ASSIGNEE_TO_OWNER: number;
                BOARD_REQUEST_COPY_WITH_COMPLETED_TRANSACTIONS: number;
                BOARD_REQUEST_FILTER: number;
                BOARD_REQUEST_FILTER_INCLUDE_TRANSACTION_TODO: number;
                BOARD_REQUEST_FILTER_INCLUDE_CUSTOM_FOLDER: number;
                BOARD_REQUEST_IS_INSTANT_MEET: number;
                SERVER_PROBE_SERVER_NAME: number;
                SERVER_OBJECT_READ_SERVER_NAME: number;
                SERVER_OBJECT_READ_LOG_SERVER_NAME: number;
                SERVER_OBJECT_FORWARD_REQUEST_TYPE: number;
                SERVER_OBJECT_GROUP_ID: number;
                SERVER_OBJECT_READ_ID: number;
                SERVER_OBJECT_READ_TYPE: number;
                SERVER_OBJECT_READ_QUERY_STRING: number;
                SERVER_OBJECT_READ_SESSION_KEY: number;
                SERVER_OBJECT_READ_USER_WITH_EMAIL: number;
                SERVER_OBJECT_READ_DEVICE_TOKEN_MAPPING: number;
                SERVER_OBJECT_READ_WITH_DETAIL: number;
                SERVER_OBJECT_WRITE_ID: number;
                SERVER_OBJECT_HASH_ID: number;
                SERVER_RESOURCE_DOWNLOAD_KEY: number;
                SERVER_RESOURCE_UPLOAD_KEY: number;
                SERVER_RESOURCE_DOWNLOAD_BUCKET: number;
                SERVER_RESOURCE_UPLOAD_BUCKET: number;
                SERVER_RESOURCE_DOWNLOAD_OBJECT_ID: number;
                SERVER_RESOURCE_UPLOAD_OBJECT_ID: number;
                SERVER_PREVIEW_RESOURCE_URL: number;
                SERVER_PREVIEW_RESOURCE_HASH: number;
                SERVER_PREVIEW_RESOURCE_EXTENSION: number;
                SERVER_REDO_JOB_ID: number;
                SERVER_MIGRATE_ZONE: number;
                SERVER_JOB_DOMAIN: number;
                SERVER_JOB_USER_ID: number;
                SERVER_JOB_FORWARDED_URI: number;
                SERVER_OBJECT_READ_PARAM_BEFORE: number;
                SERVER_OBJECT_READ_PARAM_AFTER: number;
                SERVER_OBJECT_READ_BOARD_FEEDS: number;
                SERVER_OBJECT_READ_BOARD_FEEDS_PAGE: number;
                SERVER_OBJECT_READ_BOARD_THREAD: number;
                SERVER_OBJECT_READ_BOARD_FOLDER: number;
                SERVER_OBJECT_READ_BOARD_FILE: number;
                SERVER_UPLOAD_CRASH_REPORT_NAME: number;
                SERVER_DOWNLOAD_CRASH_REPORT_NAME: number;
                SERVER_REQUEST_SERVER_TOKEN: number;
                SERVER_REQUEST_EXPIRES_AFTER: number;
                SERVER_REQUEST_SERVER_DOMAIN: number;
                GROUP_REQUEST_CANCEL_MESSAGE: number;
                GROUP_REQUEST_INVITE_PREVIEW: number;
                GROUP_REQUEST_INVITE_UPDATE_EXISTING: number;
                GROUP_REQUEST_STRIPE_SUBSCRIBE_TOKEN: number;
                GROUP_REQUEST_STRIPE_COUPON_CODE: number;
                GROUP_REQUEST_READ_MEET: number;
                GROUP_REQUEST_DELETE_BOARD: number;
                GROUP_REQUEST_READ_RELATED_USER_RELATION: number;
                GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE: number;
                GROUP_REQUEST_READ_RELATED_USER_CONTENT_LIBRARY: number;
                GROUP_REQUEST_READ_RELATED_USER_FLOW_TEMPLATE_LIBRARY: number;
                GROUP_REQUEST_NO_RELATION_BOARD: number;
                GROUP_REQUEST_CREATE_RELATION_BOARD: number;
                GROUP_REQUEST_CREATE_PENDING_RELATION: number;
                GROUP_REQUEST_WELCOME_MESSAGE: number;
                GROUP_REQUEST_JWT_EXPIRES_AFTER: number;
                GROUP_REQUEST_JWT_PAYLOAD: number;
                GROUP_REQUEST_BOX_PAYLOAD: number;
                GROUP_REQUEST_REFRESH_DEFAULT_MEET_PASSWORD: number;
                GROUP_REQUEST_CREATE_CREATOR_AS_GROUP_OWNER: number;
                GROUP_REQUEST_READ_CONTENT_LIBRARY_INCLUDE_DEFAULT: number;
                CLIENT_PARAM_REVISION: number;
                CLIENT_PARAM_JWT: number;
                CLIENT_PARAM_FROM_BOARD_ID: number;
                CLIENT_PARAM_SET_USER_ID_TO_VARIABLE: number;
                CLIENT_PARAM_CHECK_BOARD_ACCESS: number;
                CLIENT_PARAM_ORIGINAL_BOARD_ID: number;
                CLIENT_PARAM_PARENT_BOARD_ID: number;
                CLIENT_PARAM_KEEP_BOARD_OWNER: number;
                CLIENT_PARAM_EMAIL_SMS_SKIP_USER_ID: number;
                CLIENT_PARAM_CUSTOM_DATA: number;
                CLIENT_PARAM_REFERENCE_TYPE: number;
                CLIENT_PARAM_BROADCAST_TO_USER_ID: number;
                PRESENCE_PARAM_USER_ONLINE: number;
                PRESENCE_PARAM_USER_CLIENT: number;
                OUTPUT_INCLUDE_STRING: number;
                BOARD_REQUEST_ACD_SESSION_END_FEED: number;
                BOARD_REQUEST_MEETING_TRANSCRIPTION: number;
                BOARD_REQUEST_MEETING_SUMMARY: number;
                CLIENT_PARAM_TIMELINE_BOTTOM_FEED_TIMESTAMP: number;
                USER_REQUEST_READ_TIMEZONE: number;
                GROUP_REQUEST_REPORT_NAME: number;
                USER_REQUEST_INCL_WORKSPACE_TAGS: number;
                USER_REQUEST_INCL_ADDITIONAL_COLUMNS: number;
            };
        };
        ClientParam: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                string_value: {
                    type: string;
                    id: number;
                };
                uint64_value: {
                    type: string;
                    id: number;
                };
            };
        };
        ActionPageSwitch: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                page_sequence: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
            };
        };
        ActionLaserPointer: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                page_sequence: {
                    type: string;
                    id: number;
                };
                px: {
                    type: string;
                    id: number;
                };
                py: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
            };
        };
        ActionUserPointer: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                page_sequence: {
                    type: string;
                    id: number;
                };
                px: {
                    type: string;
                    id: number;
                };
                py: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        AudioWebrtcChannel: {
            edition: string;
            fields: {
                channel_id: {
                    type: string;
                    id: number;
                };
                webrtc_offer: {
                    type: string;
                    id: number;
                };
                webrtc_answer: {
                    type: string;
                    id: number;
                };
            };
        };
        AudioStatusRequest: {
            edition: string;
            values: {
                AUDIO_STATUS_REQUEST_NONE: number;
                AUDIO_STATUS_REQUEST_MUTE: number;
                AUDIO_STATUS_REQUEST_UNMUTE: number;
                AUDIO_STATUS_REQUEST_LEAVE_TELEPHONY: number;
            };
        };
        AudioStatus: {
            edition: string;
            fields: {
                is_in_session: {
                    type: string;
                    id: number;
                };
                is_mute: {
                    type: string;
                    id: number;
                };
                ssrc: {
                    type: string;
                    id: number;
                };
                channels: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        TelephoneStatus: {
            edition: string;
            fields: {
                is_in_session: {
                    type: string;
                    id: number;
                };
                is_mute: {
                    type: string;
                    id: number;
                };
                is_pure_telephone_user: {
                    type: string;
                    id: number;
                };
                ssrc: {
                    type: string;
                    id: number;
                };
                twilio_param: {
                    type: string;
                    id: number;
                };
                join_timestamp: {
                    type: string;
                    id: number;
                };
                leave_timestamp: {
                    type: string;
                    id: number;
                };
            };
        };
        RosterVideoStatus: {
            edition: string;
            fields: {
                is_in_session: {
                    type: string;
                    id: number;
                };
                is_broadcast: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardWaitingUserStatus: {
            edition: string;
            values: {
                BOARD_WAITING_USER_STATUS_NONE: number;
                BOARD_WAITING_USER_STATUS_PENDING: number;
                BOARD_WAITING_USER_STATUS_APPROVED: number;
                BOARD_WAITING_USER_STATUS_DENIED: number;
                BOARD_WAITING_USER_STATUS_BLOCKED: number;
            };
        };
        ActionUserRoster: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                requestor_id: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                group: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                audio_status: {
                    type: string;
                    id: number;
                };
                audio_status_request: {
                    type: string;
                    id: number;
                };
                waiting_user_status: {
                    type: string;
                    id: number;
                };
                is_host: {
                    type: string;
                    id: number;
                };
                is_presenter: {
                    type: string;
                    id: number;
                };
                is_invisible: {
                    type: string;
                    id: number;
                };
                is_from_team: {
                    type: string;
                    id: number;
                };
                participant_number: {
                    type: string;
                    id: number;
                };
                telephone_status: {
                    type: string;
                    id: number;
                };
                video_status: {
                    type: string;
                    id: number;
                };
                roster_tag: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
            };
        };
        ActionUserRosterEventItem: {
            edition: string;
            fields: {
                data: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        ActionUserRosterEvent: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                items: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        ActionUserRosterKeepAliveInfo: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                keepalive_timestamp: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        VideoStatus: {
            edition: string;
            values: {
                VIDEO_STOPPED: number;
                VIDEO_PLAYING: number;
                VIDEO_PAUSED: number;
            };
        };
        ActionVideoState: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                video_sequence: {
                    type: string;
                    id: number;
                };
                page_sequence: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                action_timestamp: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        AudioEdgeServer: {
            edition: string;
            fields: {
                availability_zone: {
                    type: string;
                    id: number;
                };
                server_url: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
                server_name: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                tcp_port: {
                    type: string;
                    id: number;
                };
            };
        };
        AudioConf: {
            edition: string;
            fields: {
                availability_zone: {
                    type: string;
                    id: number;
                };
                server_url: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
                server_name: {
                    type: string;
                    id: number;
                };
                geo_domain: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                tcp_port: {
                    type: string;
                    id: number;
                };
                fingerprint: {
                    type: string;
                    id: number;
                };
                edge_servers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                conf_id: {
                    type: string;
                    id: number;
                };
                token: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        TelephonyConf: {
            edition: string;
            fields: {
                telephony_domain_id: {
                    type: string;
                    id: number;
                };
                numbers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        SessionStatus: {
            edition: string;
            values: {
                SESSION_ENDED: number;
                SESSION_SCHEDULED: number;
                SESSION_STARTED: number;
                SESSION_EXPIRED: number;
            };
        };
        ExtCalType: {
            edition: string;
            values: {
                CAL_TYPE_OUTLOOK: number;
                CAL_TYPE_GOOGLE: number;
            };
        };
        VendorServiceType: {
            edition: string;
            values: {
                SERVICE_DEFAULT: number;
                SERVICE_ZOOM: number;
                SERVICE_OFFLINE: number;
                SERVICE_MSTEAM: number;
                SERVICE_OTHER: number;
            };
        };
        ActionObject: {
            edition: string;
            fields: {
                board_id: {
                    type: string;
                    id: number;
                };
                original_board_id: {
                    type: string;
                    id: number;
                };
                parent_board_id: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                session_password: {
                    type: string;
                    id: number;
                };
                password_protected: {
                    type: string;
                    id: number;
                };
                ext_cal_event_id: {
                    type: string;
                    id: number;
                };
                ext_cal_type: {
                    type: string;
                    id: number;
                };
                topic: {
                    type: string;
                    id: number;
                };
                agenda: {
                    type: string;
                    id: number;
                };
                isnote: {
                    type: string;
                    id: number;
                };
                is_private: {
                    type: string;
                    id: number;
                };
                page_switch: {
                    type: string;
                    id: number;
                };
                laser_pointer: {
                    type: string;
                    id: number;
                };
                user_pointer: {
                    rule: string;
                    type: string;
                    id: number;
                };
                team_roster: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_roster: {
                    rule: string;
                    type: string;
                    id: number;
                };
                keepalive_info: {
                    rule: string;
                    type: string;
                    id: number;
                };
                events: {
                    rule: string;
                    type: string;
                    id: number;
                };
                total_rosters: {
                    type: string;
                    id: number;
                };
                video_state: {
                    rule: string;
                    type: string;
                    id: number;
                };
                audio_conf: {
                    type: string;
                    id: number;
                };
                zone: {
                    type: string;
                    id: number;
                };
                is_expired: {
                    type: string;
                    id: number;
                };
                is_locked: {
                    type: string;
                    id: number;
                };
                last_modified_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                last_deletion_revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                ds_conf: {
                    type: string;
                    id: number;
                };
                ds_state: {
                    rule: string;
                    type: string;
                    id: number;
                };
                recording_state: {
                    rule: string;
                    type: string;
                    id: number;
                };
                audio_state: {
                    rule: string;
                    type: string;
                    id: number;
                };
                session_video_state: {
                    rule: string;
                    type: string;
                    id: number;
                };
                telephony_conf: {
                    type: string;
                    id: number;
                };
                scheduled_start_time: {
                    type: string;
                    id: number;
                };
                scheduled_end_time: {
                    type: string;
                    id: number;
                };
                session_status: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                milliseconds_allowed_to_join_before_start: {
                    type: string;
                    id: number;
                };
                record_multiple_video_channel: {
                    type: string;
                    id: number;
                };
                auto_recording: {
                    type: string;
                    id: number;
                };
                enable_ringtone: {
                    type: string;
                    id: number;
                };
                reminder_interval: {
                    type: string;
                    id: number;
                };
                vendor_service_type: {
                    type: string;
                    id: number;
                };
                vendor_start_url: {
                    type: string;
                    id: number;
                };
                vendor_join_url: {
                    type: string;
                    id: number;
                };
                vendor_meet_id: {
                    type: string;
                    id: number;
                };
                vendor_service_owner: {
                    type: string;
                    id: number;
                };
                vendor_occurrence_id: {
                    type: string;
                    id: number;
                };
                recording: {
                    type: string;
                    id: number;
                };
                transcription: {
                    type: string;
                    id: number;
                };
                transcription_vtt: {
                    type: string;
                    id: number;
                };
                meet_summary: {
                    type: string;
                    id: number;
                };
                meet_summary_edited: {
                    type: string;
                    id: number;
                };
                audio_speaker: {
                    type: string;
                    id: number;
                };
                meet_chat: {
                    type: string;
                    id: number;
                };
                timezone: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                dtstart: {
                    type: string;
                    id: number;
                };
                rrule: {
                    type: string;
                    id: number;
                };
                exdate: {
                    type: string;
                    id: number;
                };
                location: {
                    type: string;
                    id: number;
                };
                total_events: {
                    type: string;
                    id: number;
                };
                active_speaker_id: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        DesktopShareConf: {
            edition: string;
            fields: {
                server_url: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
                server_ip: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                conf_id: {
                    type: string;
                    id: number;
                };
                token: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        DesktopShareStatus: {
            edition: string;
            values: {
                DS_STOPPED: number;
                DS_STARTED: number;
                DS_PAUSED: number;
                DS_RESUMED: number;
                DS_REMOTECONTROL: number;
            };
        };
        DesktopShareState: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                page_sequence: {
                    type: string;
                    id: number;
                };
                ds_id: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                is_annotation_enabled: {
                    type: string;
                    id: number;
                };
                is_cobrowsing: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
            };
        };
        SessionRecordingStatus: {
            edition: string;
            values: {
                RECORDING_STOPPED: number;
                RECORDING_STARTED: number;
                RECORDING_PAUSED: number;
                RECORDING_RESUMED: number;
                RECORDING_SAVED: number;
                RECORDING_CANCELLED: number;
            };
        };
        SessionRecordingState: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                destination_board_id: {
                    type: string;
                    id: number;
                };
                recording_name: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        SessionAudioStatus: {
            edition: string;
            values: {
                AUDIO_STOPPED: number;
                AUDIO_STARTED: number;
            };
        };
        SessionAudioState: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        SessionVideoStatus: {
            edition: string;
            values: {
                SESSION_VIDEO_STOPPED: number;
                SESSION_VIDEO_STARTED: number;
            };
        };
        SessionVideoState: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        CapacityReport: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                audioservers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                dsservers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                videoservers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                pbxservers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        AudioServer: {
            edition: string;
            fields: {
                availability_zone: {
                    type: string;
                    id: number;
                };
                server_url: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
                server_name: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                tcp_port: {
                    type: string;
                    id: number;
                };
                capacity: {
                    type: string;
                    id: number;
                };
                keepalive_timestamp: {
                    type: string;
                    id: number;
                };
                cores: {
                    rule: string;
                    type: string;
                    id: number;
                };
                edge_servers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                recording_server_addr: {
                    type: string;
                    id: number;
                };
                internal_server_addr: {
                    type: string;
                    id: number;
                };
                fingerprint: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        AudioServerCore: {
            edition: string;
            fields: {
                total_sessions: {
                    type: string;
                    id: number;
                };
                total_rosters: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        DesktopShareServer: {
            edition: string;
            fields: {
                availability_zone: {
                    type: string;
                    id: number;
                };
                server_url: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
                server_ip: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                capacity: {
                    type: string;
                    id: number;
                };
                total_sessions: {
                    type: string;
                    id: number;
                };
                total_rosters: {
                    type: string;
                    id: number;
                };
                keepalive_timestamp: {
                    type: string;
                    id: number;
                };
                recording_server_addr: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        VideoServer: {
            edition: string;
            fields: {
                availability_zone: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                capacity: {
                    type: string;
                    id: number;
                };
                keepalive_timestamp: {
                    type: string;
                    id: number;
                };
                recording_server_addr: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PbxServer: {
            edition: string;
            fields: {
                server_addr: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        TelephoneNumber: {
            edition: string;
            fields: {
                number: {
                    type: string;
                    id: number;
                };
                location: {
                    type: string;
                    id: number;
                };
                plain_number: {
                    type: string;
                    id: number;
                };
                prompt_meetid: {
                    type: string;
                    id: number;
                };
                prompt_participantnumber: {
                    type: string;
                    id: number;
                };
                prompt_joined: {
                    type: string;
                    id: number;
                };
                prompt_left: {
                    type: string;
                    id: number;
                };
                prompt_ended: {
                    type: string;
                    id: number;
                };
                prompt_retrymeetid: {
                    type: string;
                    id: number;
                };
                prompt_invalidmeetid: {
                    type: string;
                    id: number;
                };
                prompt_notinprogress: {
                    type: string;
                    id: number;
                };
                prompt_noinput: {
                    type: string;
                    id: number;
                };
                prompt_goodbye: {
                    type: string;
                    id: number;
                };
                prompt_waiting: {
                    type: string;
                    id: number;
                };
                prompt_decline: {
                    type: string;
                    id: number;
                };
                prompt_muted: {
                    type: string;
                    id: number;
                };
                prompt_unmuted: {
                    type: string;
                    id: number;
                };
                prompt_mute_instruction: {
                    type: string;
                    id: number;
                };
                prompt_password: {
                    type: string;
                    id: number;
                };
                prompt_password_invalid: {
                    type: string;
                    id: number;
                };
                prompt_password_try_later: {
                    type: string;
                    id: number;
                };
                prompt_recording: {
                    type: string;
                    id: number;
                };
                prompt_first_attendee: {
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                is_default: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
            };
        };
        TelephonyDomainSmsProviderType: {
            edition: string;
            values: {
                SMS_PROVIDER_INVALID: number;
                SMS_PROVIDER_TWILIO: number;
            };
        };
        TelephonyDomainSmsProvider: {
            edition: string;
            fields: {
                type: {
                    type: string;
                    id: number;
                };
                twilio_api_url: {
                    type: string;
                    id: number;
                };
                twilio_account_sid: {
                    type: string;
                    id: number;
                };
                twilio_auth_token: {
                    type: string;
                    id: number;
                };
                twilio_from_number: {
                    type: string;
                    id: number;
                };
            };
        };
        TelephonyDomainPartner: {
            edition: string;
            fields: {
                partner: {
                    type: string;
                    id: number;
                };
            };
        };
        TelephonyDomain: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                numbers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sms_provider: {
                    type: string;
                    id: number;
                };
                partner: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        CacheMessage: {
            edition: string;
            fields: {
                source_id: {
                    type: string;
                    id: number;
                };
                source_session_id: {
                    type: string;
                    id: number;
                };
                destination_id: {
                    type: string;
                    id: number;
                };
                destination_session_id: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                object: {
                    type: string;
                    id: number;
                };
                apn_payload_json: {
                    type: string;
                    id: number;
                };
                gcm_payload: {
                    type: string;
                    id: number;
                };
            };
        };
        CacheObjectChange: {
            edition: string;
            fields: {
                latest_change: {
                    type: string;
                    id: number;
                };
            };
        };
        CacheObject: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                };
                group: {
                    type: string;
                    id: number;
                };
                partner: {
                    type: string;
                    id: number;
                };
                saml_service: {
                    type: string;
                    id: number;
                };
                telephony_domain: {
                    type: string;
                    id: number;
                };
                board: {
                    type: string;
                    id: number;
                };
                webapp: {
                    type: string;
                    id: number;
                };
                recording: {
                    type: string;
                    id: number;
                };
                usage: {
                    type: string;
                    id: number;
                };
                audio_report: {
                    type: string;
                    id: number;
                };
                ds_report: {
                    type: string;
                    id: number;
                };
                video_report: {
                    type: string;
                    id: number;
                };
                system_config: {
                    type: string;
                    id: number;
                };
                session: {
                    type: string;
                    id: number;
                };
                audio: {
                    type: string;
                    id: number;
                };
                presence: {
                    type: string;
                    id: number;
                };
                contacts: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                previous_revision: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                activity_logs: {
                    rule: string;
                    type: string;
                    id: number;
                };
                activity_stats: {
                    type: string;
                    id: number;
                };
                local_changes: {
                    rule: string;
                    type: string;
                    id: number;
                };
                latest_changes: {
                    rule: string;
                    type: string;
                    id: number;
                };
                request_id: {
                    type: string;
                    id: number;
                };
            };
        };
        Contacts: {
            edition: string;
            fields: {
                contacts: {
                    rule: string;
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                previous_revision: {
                    type: string;
                    id: number;
                };
                previous_server: {
                    type: string;
                    id: number;
                };
                previous_timestamp: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        Presence: {
            edition: string;
            fields: {
                issuer: {
                    type: string;
                    id: number;
                };
                connection_id: {
                    type: string;
                    id: number;
                };
                buddies: {
                    rule: string;
                    type: string;
                    id: number;
                };
                uptime: {
                    type: string;
                    id: number;
                };
            };
        };
        HttpHeader: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                string_value: {
                    type: string;
                    id: number;
                };
            };
        };
        UserType: {
            edition: string;
            options: {
                allow_alias: boolean;
            };
            values: {
                USER_TYPE_NORMAL: number;
                USER_TYPE_INVALID: number;
                USER_TYPE_DEVICE: number;
                USER_TYPE_AGENT: number;
                USER_TYPE_SSO: number;
                USER_TYPE_SERVICE: number;
                USER_TYPE_BOT: number;
                USER_TYPE_WEBAPP: number;
                USER_TYPE_LOCAL: number;
            };
        };
        UserOSType: {
            edition: string;
            values: {
                OS_TYPE_WINDOWS: number;
                OS_TYPE_WINDOWS_XP: number;
                OS_TYPE_WINDOWS_VISTA: number;
                OS_TYPE_WINDOWS_7: number;
                OS_TYPE_WINDOWS_8: number;
                OS_TYPE_MAC: number;
                OS_TYPE_MAC_10_6: number;
                OS_TYPE_MAC_10_7: number;
                OS_TYPE_MAC_10_8: number;
                OS_TYPE_MAC_10_9: number;
                OS_TYPE_MAC_10_10: number;
                OS_TYPE_IOS: number;
                OS_TYPE_ANDROID: number;
                OS_TYPE_CLOUD: number;
            };
        };
        NotificationLevel: {
            edition: string;
            values: {
                NOTIFICATION_LEVEL_ALL: number;
                NOTIFICATION_LEVEL_RELATED: number;
                NOTIFICATION_LEVEL_NOTHING: number;
            };
        };
        UserResourceType: {
            edition: string;
            values: {
                USER_RESOURCE_ORINGINAL: number;
                USER_RESOURCE_PICTURE: number;
                USER_RESOURCE_PICTURE2x: number;
                USER_RESOURCE_PICTURE4x: number;
                USER_RESOURCE_CSV_IMPORT_INPUT: number;
                USER_RESOURCE_CSV_IMPORT_OUTPUT: number;
                USER_RESOURCE_STAMP: number;
            };
        };
        UserResource: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                content_type: {
                    type: string;
                    id: number;
                };
                content_length: {
                    type: string;
                    id: number;
                };
                sha256_hash: {
                    type: string;
                    id: number;
                };
                hash: {
                    type: string;
                    id: number;
                };
                original_url: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserTag: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                string_value: {
                    type: string;
                    id: number;
                };
                uint64_value: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserBoard: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
                category_uuid: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                is_default: {
                    type: string;
                    id: number;
                };
                is_group: {
                    type: string;
                    id: number;
                };
                feed_unread_count: {
                    type: string;
                    id: number;
                };
                first_unread_feed_sequence: {
                    type: string;
                    id: number;
                };
                accessed_time: {
                    type: string;
                    id: number;
                };
                first_unread_feed_timestamp: {
                    type: string;
                    id: number;
                };
                enabled_time: {
                    type: string;
                    id: number;
                };
                dismissed_time: {
                    type: string;
                    id: number;
                };
                is_favorite: {
                    type: string;
                    id: number;
                };
                is_archive: {
                    type: string;
                    id: number;
                };
                archived_time: {
                    type: string;
                    id: number;
                };
                is_notification_off: {
                    type: string;
                    id: number;
                };
                push_notification_level: {
                    type: string;
                    id: number;
                };
                waiting_signatures: {
                    type: string;
                    id: number;
                };
                original_sequence: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserBoardCategory: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                is_archive: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserToken: {
            edition: string;
            fields: {
                token: {
                    type: string;
                    id: number;
                };
                apple_device_token: {
                    type: string;
                    id: number;
                };
                apple_voip_token: {
                    type: string;
                    id: number;
                };
                client_version: {
                    type: string;
                    id: number;
                };
                android_device_token: {
                    type: string;
                    id: number;
                };
                vendor: {
                    type: string;
                    id: number;
                };
                vendor_ext: {
                    type: string;
                    id: number;
                };
                is_access_token: {
                    type: string;
                    id: number;
                };
                is_refresh_token: {
                    type: string;
                    id: number;
                };
                access_token: {
                    type: string;
                    id: number;
                };
                client_ua: {
                    type: string;
                    id: number;
                };
                client_accept_language: {
                    type: string;
                    id: number;
                };
                client_id: {
                    type: string;
                    id: number;
                };
                uid: {
                    type: string;
                    id: number;
                };
                token_sequence: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserQRToken: {
            edition: string;
            fields: {
                token: {
                    type: string;
                    id: number;
                };
                creator: {
                    type: string;
                    id: number;
                };
                last_updated_time: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserAgent: {
            edition: string;
            fields: {
                agent: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserContactStatus: {
            edition: string;
            values: {
                CONTACT_NORMAL: number;
                CONTACT_PENDING: number;
                CONTACT_INVITED: number;
            };
        };
        UserContact: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                group: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                has_push_notification: {
                    type: string;
                    id: number;
                };
                is_private: {
                    type: string;
                    id: number;
                };
                is_from_team: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserRole: {
            edition: string;
            values: {
                USER_ROLE_NORMAL: number;
                USER_ROLE_CRASH_REPORT_READ: number;
                USER_ROLE_SERVER_STATUS_READ: number;
                USER_ROLE_SUPERADMIN_READONLY: number;
                USER_ROLE_SUPERADMIN: number;
                USER_ROLE_OBJECT_READ: number;
                USER_ROLE_OBJECT_WRITE: number;
            };
        };
        UserLevel: {
            edition: string;
            values: {
                USER_LEVEL_FREE: number;
                USER_LEVEL_TRIAL: number;
                USER_LEVEL_PRO: number;
                USER_LEVEL_BETA: number;
            };
        };
        UserCap: {
            edition: string;
            fields: {
                user_boards_max: {
                    type: string;
                    id: number;
                };
                open_flow_boards_max: {
                    type: string;
                    id: number;
                };
                board_users_max: {
                    type: string;
                    id: number;
                };
                board_pages_max: {
                    type: string;
                    id: number;
                };
                board_history_max: {
                    type: string;
                    id: number;
                };
                board_notification_max: {
                    type: string;
                    id: number;
                };
                session_users_max: {
                    type: string;
                    id: number;
                };
                user_cloud_max: {
                    type: string;
                    id: number;
                };
                client_max_body_size: {
                    type: string;
                    id: number;
                };
                client_allowed_file_types: {
                    rule: string;
                    type: string;
                    id: number;
                };
                meet_duration_max: {
                    type: string;
                    id: number;
                };
                allow_meet_recording: {
                    type: string;
                    id: number;
                };
                allow_meet_voip: {
                    type: string;
                    id: number;
                };
                allow_meet_telephony: {
                    type: string;
                    id: number;
                };
                user_agent_max: {
                    type: string;
                    id: number;
                };
                user_integrations_max: {
                    type: string;
                    id: number;
                };
                group_boards_max: {
                    type: string;
                    id: number;
                };
                team_users_max: {
                    type: string;
                    id: number;
                };
                user_relations_max: {
                    type: string;
                    id: number;
                };
            };
        };
        UserFavorite: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserMentionMe: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        CallType: {
            edition: string;
            values: {
                CALL_TYPE_INVALID: number;
                CALL_TYPE_OUTBOUND: number;
                CALL_TYPE_INBOUND: number;
            };
        };
        CallStatus: {
            edition: string;
            values: {
                CALL_STATUS_INVALID: number;
                CALL_STATUS_INITIALIZED: number;
                CALL_STATUS_RINGING: number;
                CALL_STATUS_CONNECTING: number;
                CALL_STATUS_CONNECTED: number;
                CALL_STATUS_CANCELLED: number;
                CALL_STATUS_NOANSWER: number;
                CALL_STATUS_DECLINED: number;
                CALL_STATUS_ENDED: number;
                CALL_STATUS_FAILED: number;
            };
        };
        ClientType: {
            edition: string;
            values: {
                CLIENT_TYPE_INVALID: number;
                CLIENT_TYPE_UC: number;
                CLIENT_TYPE_PHONE: number;
            };
        };
        CallUser: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                client_type: {
                    type: string;
                    id: number;
                };
                sip_address: {
                    type: string;
                    id: number;
                };
                call_sequence: {
                    type: string;
                    id: number;
                };
            };
        };
        UserCallLog: {
            edition: string;
            fields: {
                call_id: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                peer: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                board_id: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                session_id: {
                    type: string;
                    id: number;
                };
                client_type: {
                    type: string;
                    id: number;
                };
                sip_call_id: {
                    type: string;
                    id: number;
                };
                dtmf_digits: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        ACDType: {
            edition: string;
            values: {
                ACD_TYPE_INVALID: number;
                ACD_TYPE_CHAT: number;
                ACD_TYPE_MEET: number;
            };
        };
        ACDStatus: {
            edition: string;
            values: {
                ACD_STATUS_INVALID: number;
                ACD_STATUS_INITIALIZED: number;
                ACD_STATUS_QUEUED: number;
                ACD_STATUS_CONNECTING: number;
                ACD_STATUS_CONNECTED: number;
                ACD_STATUS_CANCELLED: number;
                ACD_STATUS_NOANSWER: number;
                ACD_STATUS_DECLINED: number;
                ACD_STATUS_ENDED: number;
                ACD_STATUS_FAILED: number;
            };
        };
        UserACDLog: {
            edition: string;
            fields: {
                call_id: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                peer: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                board_id: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserConnection: {
            edition: string;
            fields: {
                session_id: {
                    type: string;
                    id: number;
                };
                connection_id: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        DateRange: {
            edition: string;
            fields: {
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
            };
        };
        OutOfOfficeStatus: {
            edition: string;
            fields: {
                status: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                message: {
                    type: string;
                    id: number;
                };
                backup: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                r_out_of_office: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        UserRelationStatus: {
            edition: string;
            values: {
                RELATION_INIT: number;
                RELATION_PENDING: number;
                RELATION_NORMAL: number;
            };
        };
        UserRelation: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                invitation_code: {
                    type: string;
                    id: number;
                };
                line_invitation_code: {
                    type: string;
                    id: number;
                };
                whatsapp_invitation_code: {
                    type: string;
                    id: number;
                };
                whatsapp_chat_id: {
                    type: string;
                    id: number;
                };
                whatsapp_chat_status: {
                    type: string;
                    id: number;
                };
                whatsapp_invitation_url: {
                    type: string;
                    id: number;
                };
                invited_time: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BotUserRelation: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        HashAlgorithm: {
            edition: string;
            values: {
                HASH_ALGORITHM_SHA256: number;
                HASH_ALGORITHM_PKCS5_PBKDF2_SHA256: number;
            };
        };
        SignatureStyle: {
            edition: string;
            values: {
                SIGNATURE_STYLE_NONE: number;
                SIGNATURE_STYLE_PRESELECTED: number;
                SIGNATURE_STYLE_DRAWN_ON_DEVICE: number;
            };
        };
        UserDevice: {
            edition: string;
            fields: {
                device_id: {
                    type: string;
                    id: number;
                };
                client_ua: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserNotification: {
            edition: string;
            fields: {
                payload: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardMemberNotificationSetting: {
            edition: string;
            fields: {
                not_join_within: {
                    type: string;
                    id: number;
                };
                not_join_within_to_owner: {
                    type: string;
                    id: number;
                };
            };
        };
        ActionNotificationSetting: {
            edition: string;
            fields: {
                on_due: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                before_due: {
                    type: string;
                    id: number;
                };
                after_due: {
                    type: string;
                    id: number;
                };
                after_due_repeat: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardNotificationSetting: {
            edition: string;
            fields: {
                on_due: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                before_due: {
                    type: string;
                    id: number;
                };
                after_due: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                after_due_repeat: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
            };
        };
        User: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                first_name: {
                    type: string;
                    id: number;
                };
                last_name: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                phone_number: {
                    type: string;
                    id: number;
                };
                online: {
                    type: string;
                    id: number;
                };
                in_meet: {
                    type: string;
                    id: number;
                };
                os_type: {
                    type: string;
                    id: number;
                };
                disabled: {
                    type: string;
                    id: number;
                };
                passcode_protected: {
                    type: string;
                    id: number;
                };
                email: {
                    type: string;
                    id: number;
                };
                unique_id: {
                    type: string;
                    id: number;
                };
                apple_device_token: {
                    type: string;
                    id: number;
                };
                agent_user_id: {
                    type: string;
                    id: number;
                };
                work_phone_number: {
                    type: string;
                    id: number;
                };
                sip_configuration: {
                    type: string;
                    id: number;
                };
                extension_phone_number: {
                    type: string;
                    id: number;
                };
                title: {
                    type: string;
                    id: number;
                };
                display_email: {
                    type: string;
                    id: number;
                };
                display_phone_number: {
                    type: string;
                    id: number;
                };
                display_id: {
                    type: string;
                    id: number;
                };
                pass: {
                    type: string;
                    id: number;
                };
                hashed_pass: {
                    type: string;
                    id: number;
                };
                pass_algorithm: {
                    type: string;
                    id: number;
                };
                old_pass: {
                    type: string;
                    id: number;
                };
                address: {
                    type: string;
                    id: number;
                };
                email_onboarded: {
                    type: string;
                    id: number;
                };
                tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                qr_tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                email_verification_token: {
                    type: string;
                    id: number;
                };
                email_verification_code: {
                    type: string;
                    id: number;
                };
                code_updated_time: {
                    type: string;
                    id: number;
                };
                failed_attempts: {
                    type: string;
                    id: number;
                };
                last_login_timestamp: {
                    type: string;
                    id: number;
                };
                reset_password_timestamps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                lookup_domain_timestamps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                email_verified: {
                    type: string;
                    id: number;
                };
                token_updated_time: {
                    type: string;
                    id: number;
                };
                picture: {
                    type: string;
                    id: number;
                };
                picture2x: {
                    type: string;
                    id: number;
                };
                picture4x: {
                    type: string;
                    id: number;
                };
                picture_path: {
                    type: string;
                    id: number;
                };
                picture2x_path: {
                    type: string;
                    id: number;
                };
                picture4x_path: {
                    type: string;
                    id: number;
                };
                picture_url: {
                    type: string;
                    id: number;
                };
                android_device_token: {
                    type: string;
                    id: number;
                };
                apple_voip_token: {
                    type: string;
                    id: number;
                };
                ios_app_id: {
                    type: string;
                    id: number;
                };
                android_app_pkg_name: {
                    type: string;
                    id: number;
                };
                boards: {
                    rule: string;
                    type: string;
                    id: number;
                };
                personal_rooms: {
                    rule: string;
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                tags: {
                    rule: string;
                    type: string;
                    id: number;
                };
                agents: {
                    rule: string;
                    type: string;
                    id: number;
                };
                call_logs: {
                    rule: string;
                    type: string;
                    id: number;
                };
                acd_logs: {
                    rule: string;
                    type: string;
                    id: number;
                };
                group_boards: {
                    rule: string;
                    type: string;
                    id: number;
                };
                role: {
                    type: string;
                    id: number;
                };
                level: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                cap: {
                    type: string;
                    id: number;
                };
                total_cloud_size: {
                    type: string;
                    id: number;
                };
                boards_owned: {
                    type: string;
                    id: number;
                };
                boards_invited: {
                    type: string;
                    id: number;
                };
                boards_total: {
                    type: string;
                    id: number;
                };
                boards_owned_pages: {
                    type: string;
                    id: number;
                };
                boards_owned_comments: {
                    type: string;
                    id: number;
                };
                boards_owned_todos: {
                    type: string;
                    id: number;
                };
                boards_owned_invitees: {
                    type: string;
                    id: number;
                };
                meet_hosted: {
                    type: string;
                    id: number;
                };
                meet_invited: {
                    type: string;
                    id: number;
                };
                contacts_total: {
                    type: string;
                    id: number;
                };
                relations_total: {
                    type: string;
                    id: number;
                };
                feed_unread_total: {
                    type: string;
                    id: number;
                };
                feed_unread_sr: {
                    type: string;
                    id: number;
                };
                agents_total: {
                    type: string;
                    id: number;
                };
                accessed_time: {
                    type: string;
                    id: number;
                };
                last_archive_time: {
                    type: string;
                    id: number;
                };
                last_active_time: {
                    type: string;
                    id: number;
                };
                groups: {
                    rule: string;
                    type: string;
                    id: number;
                };
                webapps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                partners: {
                    rule: string;
                    type: string;
                    id: number;
                };
                managed_teams: {
                    rule: string;
                    type: string;
                    id: number;
                };
                collab_teams: {
                    rule: string;
                    type: string;
                    id: number;
                };
                categories: {
                    rule: string;
                    type: string;
                    id: number;
                };
                feeds: {
                    rule: string;
                    type: string;
                    id: number;
                };
                enable_notification_emails: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_digest_email: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                last_digest_email_timestamp: {
                    type: string;
                    id: number;
                };
                timezone: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                language: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                contacts: {
                    rule: string;
                    type: string;
                    id: number;
                };
                customized_presence_status: {
                    type: string;
                    id: number;
                };
                customized_presence_message: {
                    type: string;
                    id: number;
                };
                has_push_notification: {
                    type: string;
                    id: number;
                };
                collaborators: {
                    rule: string;
                    type: string;
                    id: number;
                };
                connections: {
                    rule: string;
                    type: string;
                    id: number;
                };
                relations: {
                    rule: string;
                    type: string;
                    id: number;
                };
                bot_relations: {
                    rule: string;
                    type: string;
                    id: number;
                };
                board_notification_level: {
                    type: string;
                    id: number;
                };
                session_notification_level: {
                    type: string;
                    id: number;
                };
                board_member_notification_settings: {
                    type: string;
                    id: number;
                };
                action_notification_settings: {
                    type: string;
                    id: number;
                };
                board_notification_settings: {
                    type: string;
                    id: number;
                };
                favorites: {
                    rule: string;
                    type: string;
                    id: number;
                };
                mentionmes: {
                    rule: string;
                    type: string;
                    id: number;
                };
                action_items: {
                    rule: string;
                    type: string;
                    id: number;
                };
                action_accessed_time: {
                    type: string;
                    id: number;
                };
                notifications: {
                    rule: string;
                    type: string;
                    id: number;
                };
                notification_accessed_time: {
                    type: string;
                    id: number;
                };
                broadcasts: {
                    rule: string;
                    type: string;
                    id: number;
                };
                ext_broadcasts: {
                    rule: string;
                    type: string;
                    id: number;
                };
                keep_short: {
                    type: string;
                    id: number;
                };
                sip_registration_status: {
                    type: string;
                    id: number;
                };
                sip_registration_message: {
                    type: string;
                    id: number;
                };
                sip_unread_voice_mail_count: {
                    type: string;
                    id: number;
                };
                sip_has_unread_voice_mail: {
                    type: string;
                    id: number;
                };
                signature: {
                    type: string;
                    id: number;
                };
                initials: {
                    type: string;
                    id: number;
                };
                initials_text: {
                    type: string;
                    id: number;
                };
                legal_name: {
                    type: string;
                    id: number;
                };
                signature_style: {
                    type: string;
                    id: number;
                };
                signature_path: {
                    type: string;
                    id: number;
                };
                initials_path: {
                    type: string;
                    id: number;
                };
                out_of_office: {
                    type: string;
                    id: number;
                };
                social: {
                    type: string;
                    id: number;
                };
                social_id: {
                    type: string;
                    id: number;
                };
                social_avatar: {
                    type: string;
                    id: number;
                };
                social_status: {
                    type: string;
                    id: number;
                };
                invitation_code: {
                    type: string;
                    id: number;
                };
                connector: {
                    type: string;
                    id: number;
                };
                social_updated_time: {
                    type: string;
                    id: number;
                };
                line_social_id: {
                    type: string;
                    id: number;
                };
                line_social_avatar: {
                    type: string;
                    id: number;
                };
                line_social_status: {
                    type: string;
                    id: number;
                };
                line_invitation_code: {
                    type: string;
                    id: number;
                };
                line_connector: {
                    type: string;
                    id: number;
                };
                whatsapp_social_id: {
                    type: string;
                    id: number;
                };
                whatsapp_social_avatar: {
                    type: string;
                    id: number;
                };
                whatsapp_social_status: {
                    type: string;
                    id: number;
                };
                whatsapp_invitation_code: {
                    type: string;
                    id: number;
                };
                whatsapp_connector: {
                    type: string;
                    id: number;
                };
                custom1: {
                    type: string;
                    id: number;
                };
                custom2: {
                    type: string;
                    id: number;
                };
                custom3: {
                    type: string;
                    id: number;
                };
                user_devices: {
                    rule: string;
                    type: string;
                    id: number;
                };
                flow_templates: {
                    rule: string;
                    type: string;
                    id: number;
                };
                weekdays: {
                    rule: string;
                    type: string;
                    id: number;
                };
                special_days: {
                    rule: string;
                    type: string;
                    id: number;
                };
                meeting_buffer_time: {
                    type: string;
                    id: number;
                };
                self_service_templates: {
                    rule: string;
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupUserStatus: {
            edition: string;
            values: {
                GROUP_INVALID: number;
                GROUP_INVITED: number;
                GROUP_MEMBER: number;
            };
        };
        GroupAccessType: {
            edition: string;
            values: {
                GROUP_NO_ACCESS: number;
                GROUP_READONLY_ACCESS: number;
                GROUP_MEMBER_ACCESS: number;
                GROUP_ADMIN_READONLY_ACCESS: number;
                GROUP_ADMIN_ACCESS: number;
                GROUP_OWNER_ACCESS: number;
            };
        };
        UserBroadcast: {
            edition: string;
            fields: {
                title: {
                    type: string;
                    id: number;
                };
                contacts: {
                    rule: string;
                    type: string;
                    id: number;
                };
                msg_count: {
                    type: string;
                    id: number;
                };
                msg_sent_timestamp: {
                    type: string;
                    id: number;
                };
                last_modified_timestamp: {
                    type: string;
                    id: number;
                };
                is_default: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BroadcastStatus: {
            edition: string;
            values: {
                BROADCAST_QUEUED: number;
                BROADCAST_SCHEDULED: number;
                BROADCAST_PROCESSING: number;
                BROADCAST_COMPLETED: number;
            };
        };
        BroadcastChannel: {
            edition: string;
            values: {
                BROADCAST_CHANNEL_WORKSPACE: number;
                BROADCAST_CHANNEL_INBOX: number;
            };
        };
        BroadcastTarget: {
            edition: string;
            values: {
                BROADCAST_TARGET_USER_LISTS: number;
                BROADCAST_TARGET_SPECIFIC_USERS: number;
                BROADCAST_TARGET_MY_CLIENTS: number;
                BROADCAST_TARGET_ALL_CLIENTS: number;
                BROADCAST_TARGET_CDL: number;
            };
        };
        BoardBroadcast: {
            edition: string;
            fields: {
                user_list: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                target: {
                    type: string;
                    id: number;
                };
                channel: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserGroup: {
            edition: string;
            fields: {
                group: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                role: {
                    type: string;
                    id: number;
                };
                roles: {
                    rule: string;
                    type: string;
                    id: number;
                };
                onboarded_time: {
                    type: string;
                    id: number;
                };
                group_sequence: {
                    type: string;
                    id: number;
                };
                member_alias: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                tac_agreed_time: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UserPartner: {
            edition: string;
            fields: {
                partner: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupUser: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                onboarded_time: {
                    type: string;
                    id: number;
                };
                alias: {
                    type: string;
                    id: number;
                };
                is_invalid: {
                    type: string;
                    id: number;
                };
                message: {
                    type: string;
                    id: number;
                };
                invalid_fields: {
                    rule: string;
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                role: {
                    type: string;
                    id: number;
                };
                roles: {
                    rule: string;
                    type: string;
                    id: number;
                };
                role_assigned_time: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupBoard: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupEmailConfig: {
            edition: string;
            fields: {
                username: {
                    type: string;
                    id: number;
                };
                password: {
                    type: string;
                    id: number;
                };
                server_address: {
                    type: string;
                    id: number;
                };
                server_port: {
                    type: string;
                    id: number;
                };
                from_address: {
                    type: string;
                    id: number;
                };
                is_ssl: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupIntegrationType: {
            edition: string;
            values: {
                GROUP_INTEGRATION_SAML: number;
                GROUP_INTEGRATION_APP: number;
                GROUP_INTEGRATION_LOCAL: number;
                GROUP_INTEGRATION_WECHAT: number;
                GROUP_INTEGRATION_WHATSAPP: number;
                GROUP_INTEGRATION_LINE: number;
                GROUP_INTEGRATION_SMTP: number;
                GROUP_INTEGRATION_CRM: number;
                GROUP_INTEGRATION_OUTGOING: number;
            };
        };
        GroupIntegration: {
            edition: string;
            fields: {
                type: {
                    type: string;
                    id: number;
                };
                enable_auto_provision: {
                    type: string;
                    id: number;
                };
                webapp: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                };
                group: {
                    type: string;
                    id: number;
                };
                board: {
                    type: string;
                    id: number;
                };
                is_free_subscription: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
                idp_conf: {
                    type: string;
                    id: number;
                };
                saml_user_role_types: {
                    rule: string;
                    type: string;
                    id: number;
                };
                domain: {
                    type: string;
                    id: number;
                };
                email_config: {
                    type: string;
                    id: number;
                };
                connector: {
                    type: string;
                    id: number;
                };
                crm_configuration: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupSubscriptionStatus: {
            edition: string;
            values: {
                GROUP_TRIAL_SUBSCRIPTION: number;
                GROUP_NORMAL_SUBSCRIPTION: number;
                GROUP_PAST_DUE_SUBSCRIPTION: number;
                GROUP_CANCELED_SUBSCRIPTION: number;
                GROUP_EXPIRED_SUBSCRIPTION: number;
                GROUP_DISABLED_SUBSCRIPTION: number;
            };
        };
        GroupContact: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
            };
        };
        GroupSupport: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
            };
        };
        GroupPartner: {
            edition: string;
            fields: {
                partner: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
            };
        };
        GroupCap: {
            edition: string;
            fields: {
                is_enterprise: {
                    type: string;
                    id: number;
                };
                has_sip: {
                    type: string;
                    id: number;
                };
                has_board_owner_delegate: {
                    type: string;
                    id: number;
                };
                has_email_privacy: {
                    type: string;
                    id: number;
                };
                has_push_privacy: {
                    type: string;
                    id: number;
                };
                has_user_archive: {
                    type: string;
                    id: number;
                };
                has_saml: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                is_online_billing: {
                    type: string;
                    id: number;
                };
                hide_moxtra_logo: {
                    type: string;
                    id: number;
                };
                has_app_portal: {
                    type: string;
                    id: number;
                };
                has_app_subscription: {
                    type: string;
                    id: number;
                };
                has_audit: {
                    type: string;
                    id: number;
                };
                has_org_report: {
                    type: string;
                    id: number;
                };
                has_read_receipt: {
                    type: string;
                    id: number;
                };
                ignore_email_verification: {
                    type: string;
                    id: number;
                };
                has_branding: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                has_configuration: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                disable_invite_client: {
                    type: string;
                    id: number;
                };
                enable_acd: {
                    type: string;
                    id: number;
                };
                enable_service_request: {
                    type: string;
                    id: number;
                };
                enable_phone_number_sign_up: {
                    type: string;
                    id: number;
                };
                primary_sign_up_phone_number: {
                    type: string;
                    id: number;
                };
                enable_channel_subscription: {
                    type: string;
                    id: number;
                };
                enable_direct_invitation: {
                    type: string;
                    id: number;
                };
                enable_hard_deletion: {
                    type: string;
                    id: number;
                };
                hide_saml_configuration: {
                    type: string;
                    id: number;
                };
                disable_user_creation: {
                    type: string;
                    id: number;
                };
                enable_sfdc_integration: {
                    type: string;
                    id: number;
                };
                enable_globalrelay_integration: {
                    type: string;
                    id: number;
                };
                enable_hubspot_integration: {
                    type: string;
                    id: number;
                };
                enable_dynamics_integration: {
                    type: string;
                    id: number;
                };
                enable_filevine_integration: {
                    type: string;
                    id: number;
                };
                enable_redtail_integration: {
                    type: string;
                    id: number;
                };
                enable_zoho_integration: {
                    type: string;
                    id: number;
                };
                enable_smarsh_integration: {
                    type: string;
                    id: number;
                };
                enable_advisorengine_integration: {
                    type: string;
                    id: number;
                };
                enable_meeting_audio_ringtone: {
                    type: string;
                    id: number;
                };
                enable_meeting_transcription: {
                    type: string;
                    id: number;
                };
                enable_xero_integration: {
                    type: string;
                    id: number;
                };
                enable_wealthbox_integration: {
                    type: string;
                    id: number;
                };
                enable_referrer_check: {
                    type: string;
                    id: number;
                };
                enable_access_control_allow_origin: {
                    type: string;
                    id: number;
                };
                enable_xsrf_token: {
                    type: string;
                    id: number;
                };
                enable_2fa: {
                    type: string;
                    id: number;
                };
                enable_2fa_trust_device: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                keep_identification_for_deleted_user: {
                    type: string;
                    id: number;
                };
                enforce_logout_account_when_quiting_app: {
                    type: string;
                    id: number;
                };
                enable_client_delete_account: {
                    type: string;
                    id: number;
                };
                enable_apple_sign_in: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_google_sign_in: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_salesforce_sign_in: {
                    type: string;
                    id: number;
                };
                enforce_password_login: {
                    type: string;
                    id: number;
                };
                share_org_flow_templates: {
                    type: string;
                    id: number;
                };
                is_freemium: {
                    type: string;
                    id: number;
                };
                share_org_content_library: {
                    type: string;
                    id: number;
                };
                enable_custom_smtp_integration: {
                    type: string;
                    id: number;
                };
                enable_new_frame: {
                    type: string;
                    id: number;
                };
                enable_chat_workspace: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                hide_client_dashboard: {
                    type: string;
                    id: number;
                };
                server_session_timeout_interval: {
                    type: string;
                    id: number;
                };
                restrict_new_message_email_sms: {
                    type: string;
                    id: number;
                };
                enable_share_link: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
            };
        };
        GroupSetting: {
            edition: string;
            fields: {
                content_editable_interval: {
                    type: string;
                    id: number;
                };
                content_editable_interval_for_client: {
                    type: string;
                    id: number;
                };
                org_invitation_expiry: {
                    type: string;
                    id: number;
                };
                enable_private_meet: {
                    type: string;
                    id: number;
                };
                hide_meet_recording: {
                    type: string;
                    id: number;
                };
                disable_meet_recording_sharing: {
                    type: string;
                    id: number;
                };
                enable_meet_auto_recording: {
                    type: string;
                    id: number;
                };
                enable_meet_password: {
                    type: string;
                    id: number;
                };
                meeting_default_password: {
                    type: string;
                    id: number;
                };
                enable_mobile_web_meeting_join: {
                    type: string;
                    id: number;
                };
                enable_workflow_event: {
                    type: string;
                    id: number;
                };
                enable_digest_email: {
                    type: string;
                    id: number;
                };
                digest_email_start_timestamp: {
                    type: string;
                    id: number;
                };
                digest_email_interval: {
                    type: string;
                    id: number;
                };
                enable_client_self_signup: {
                    type: string;
                    id: number;
                };
                send_service_request_on_client_self_signup: {
                    type: string;
                    id: number;
                };
                enable_client_group: {
                    type: string;
                    id: number;
                };
                enable_content_library: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_client_resources: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_action_library: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_broadcast: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                user_logins_max: {
                    type: string;
                    id: number;
                };
                expose_contact_info_to_clients: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                expose_client_contact_info_to_internals: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_flow_template_library: {
                    type: string;
                    id: number;
                };
                account_lock_duration: {
                    type: string;
                    id: number;
                };
                enforce_signature_jwt_validation_for_client_users: {
                    type: string;
                    id: number;
                };
                enable_magic_link: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_magic_link_for_internal_users: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                binder_view_token_timeout: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                binder_view_token_timeout_for_internal_users: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                use_browser_open_jwt: {
                    type: string;
                    id: number;
                };
                user_boards_auto_archive_threshold: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                terms_group_name: {
                    type: string;
                    id: number;
                };
                enable_meet_log_uploading: {
                    type: string;
                    id: number;
                };
                enable_user_data_downloading: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                include_coc_in_signed_file: {
                    type: string;
                    id: number;
                };
                enable_workspace_report_auditing: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                enable_sms_for_email_based_client_user: {
                    type: string;
                    id: number;
                };
                enable_client_distribution_list: {
                    type: string;
                    id: number;
                };
                enable_broadcast_recipient: {
                    type: string;
                    id: number;
                };
                enable_inbox: {
                    type: string;
                    id: number;
                };
                enable_ai: {
                    type: string;
                    id: number;
                };
                enable_client_group_to_internal_users: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupType: {
            edition: string;
            values: {
                GROUP_TYPE_ORG: number;
                GROUP_TYPE_TEAM: number;
                GROUP_TYPE_CLIENT_TEAM: number;
                GROUP_TYPE_TEAM_FLEXIBLE: number;
            };
        };
        GroupPlanCode: {
            edition: string;
            fields: {
                plan_code: {
                    type: string;
                    id: number;
                };
                plan_quantity: {
                    type: string;
                    id: number;
                };
                is_capped: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupTelephonyDomain: {
            edition: string;
            fields: {
                telephony_domain: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
            };
        };
        CachePlan: {
            edition: string;
            fields: {
                plan_code: {
                    type: string;
                    id: number;
                };
                plan_cap: {
                    type: string;
                    id: number;
                };
                trial_end_interval: {
                    type: string;
                    id: number;
                };
                plan_code_free_user: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                plan_code_trial_user: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                plan_code_pro_user: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                plan_code_beta_user: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardMemberPrivileges: {
            edition: string;
            values: {
                BOARD_DELETE_OTHERS_MSG: number;
                BOARD_DELETE_OTHERS_COMMENT: number;
                BOARD_DELETE_OTHERS_FILE: number;
                BOARD_DELETE_OTHERS_ANNOTATION: number;
                BOARD_HISTORY_FROM_JOIN: number;
                BOARD_INVITE_BOARD_MEMBER: number;
                BOARD_SHARE_CONTENT: number;
                BOARD_SEND_MSG: number;
                BOARD_ADD_COMMENT: number;
                BOARD_UPLOAD_FILE: number;
                BOARD_SIGN_FILE: number;
                BOARD_ADD_ANNOTATION: number;
                BOARD_COPY_TO_OTHER_BINDER: number;
                BOARD_SAVE_TO_ALBUM: number;
            };
        };
        GroupUserRoleType: {
            edition: string;
            values: {
                ROLE_TYPE_NORMAL: number;
                ROLE_TYPE_LOCAL: number;
                ROLE_TYPE_GUEST: number;
            };
        };
        ChatPrivilege: {
            edition: string;
            fields: {
                can_start_chat: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_invite_from_contact: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_invite_by_email: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_select_delegate: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_send_message: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_show_read_receipt: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_owner_remove_member: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
            };
        };
        MeetPrivilege: {
            edition: string;
            fields: {
                can_start_instant_meet: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_scheduled_meet: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_start_video: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_share_screen: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_share_file: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_do_co_browsing: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_turn_on_camera: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_download_and_share_meeting_recording: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
            };
        };
        RelationPrivilege: {
            edition: string;
            fields: {
                can_add_local_user: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
            };
        };
        FilePrivilege: {
            edition: string;
            fields: {
                can_add_file: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_clip: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_share_publiclink: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_share_internally: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_note: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_location: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_download_file: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_whiteboard: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_add_spot_comment: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_sign: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_sign_files: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_folder: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_todo: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_approval: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_acknowledge: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_file_request: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_meet_request: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_form: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_pdf_form: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_launch_web_app: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_rename_file: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_move_file: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
            };
        };
        RoutingPrivilege: {
            edition: string;
            fields: {
                can_accept_acd: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_accept_service_request: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
            };
        };
        GroupPrivilege: {
            edition: string;
            fields: {
                can_audit_user: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_manage_subscription_channel: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_access_org_report: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_manage_content_library: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_manage_client_resources: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_manage_workflow: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                can_create_shared_flow_template: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
            };
        };
        ContactPrivilege: {
            edition: string;
            fields: {
                can_access_business_directory: {
                    type: string;
                    id: number;
                };
                can_view_all_clients: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupRoleCategory: {
            edition: string;
            values: {
                ROLE_CATEGORY_NONE: number;
                ROLE_CATEGORY_DISTRIBUTION_LIST: number;
            };
        };
        GroupUserRole: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                is_default: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                include_all_admins: {
                    type: string;
                    id: number;
                };
                include_all_internal_users: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
                chat: {
                    type: string;
                    id: number;
                };
                meet: {
                    type: string;
                    id: number;
                };
                relation: {
                    type: string;
                    id: number;
                };
                file: {
                    type: string;
                    id: number;
                };
                routing: {
                    type: string;
                    id: number;
                };
                audit: {
                    type: string;
                    id: number;
                };
                contact: {
                    type: string;
                    id: number;
                };
                role_template: {
                    type: string;
                    id: number;
                };
                users_total: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        AsyncTaskStatus: {
            edition: string;
            values: {
                ASYNC_TASK_STATUS_INVALID: number;
                ASYNC_TASK_STATUS_QUEUED: number;
                ASYNC_TASK_STATUS_IN_PROGRESS: number;
                ASYNC_TASK_STATUS_SUCCESS: number;
                ASYNC_TASK_STATUS_FAILED: number;
            };
        };
        AsyncTask: {
            edition: string;
            fields: {
                request: {
                    type: string;
                    id: number;
                };
                input_resource: {
                    type: string;
                    id: number;
                };
                actor: {
                    type: string;
                    id: number;
                };
                token: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                message: {
                    type: string;
                    id: number;
                };
                detail_code: {
                    type: string;
                    id: number;
                };
                detail_message: {
                    type: string;
                    id: number;
                };
                processed_items: {
                    type: string;
                    id: number;
                };
                total_items: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupAppConfig: {
            edition: string;
            fields: {
                ios_app_version_recommended: {
                    type: string;
                    id: number;
                };
                android_app_version_recommended: {
                    type: string;
                    id: number;
                };
            };
        };
        RoutingChannel: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                picture: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                teams: {
                    rule: string;
                    type: string;
                    id: number;
                };
                include_all_admins: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                };
                board: {
                    type: string;
                    id: number;
                };
                unassigned_client_only: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        RoutingWeekday: {
            edition: string;
            fields: {
                day_of_week: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                is_close: {
                    type: string;
                    id: number;
                };
            };
        };
        RoutingSpecialDay: {
            edition: string;
            fields: {
                date: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                is_close: {
                    type: string;
                    id: number;
                };
            };
        };
        RoutingConfig: {
            edition: string;
            fields: {
                acd_max_conns_per_agent: {
                    type: string;
                    id: number;
                };
                acd_channels: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sr_channels: {
                    rule: string;
                    type: string;
                    id: number;
                };
                weekdays: {
                    rule: string;
                    type: string;
                    id: number;
                };
                special_days: {
                    rule: string;
                    type: string;
                    id: number;
                };
                acd_connection_timeout: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                template_messages: {
                    rule: string;
                    type: string;
                    id: number;
                };
                template_messages_sr: {
                    rule: string;
                    type: string;
                    id: number;
                };
                prompt_leave_message: {
                    type: string;
                    id: number;
                };
                disable_acd_leave_message: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupUserSetting: {
            edition: string;
            fields: {
                include_all_admins: {
                    type: string;
                    id: number;
                };
                include_all_internal_users: {
                    type: string;
                    id: number;
                };
            };
        };
        PropertyType: {
            edition: string;
            values: {
                PROPERTY_TYPE_INVALID: number;
                PROPERTY_TYPE_LIST: number;
                PROPERTY_TYPE_TEXT: number;
            };
        };
        Property: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                options: {
                    rule: string;
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        Group: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                alias: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                timezone: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                contact: {
                    type: string;
                    id: number;
                };
                group_caps: {
                    type: string;
                    id: number;
                };
                group_settings: {
                    type: string;
                    id: number;
                };
                support: {
                    type: string;
                    id: number;
                };
                client_support: {
                    type: string;
                    id: number;
                };
                plan_code: {
                    type: string;
                    id: number;
                };
                plan_code_local: {
                    type: string;
                    id: number;
                };
                plan_quantity: {
                    type: string;
                    id: number;
                };
                plan_quantity_local: {
                    type: string;
                    id: number;
                };
                members: {
                    rule: string;
                    type: string;
                    id: number;
                };
                managers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                managers_setting: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                template_name: {
                    type: string;
                    id: number;
                };
                customer_id: {
                    type: string;
                    id: number;
                };
                coupon_id: {
                    type: string;
                    id: number;
                };
                cancel_subscription_at_period_end: {
                    type: string;
                    id: number;
                };
                scheduled_plan_code: {
                    type: string;
                    id: number;
                };
                trial_start_time: {
                    type: string;
                    id: number;
                };
                trial_end_time: {
                    type: string;
                    id: number;
                };
                commitment_end_time: {
                    type: string;
                    id: number;
                };
                cancellation_request_time: {
                    type: string;
                    id: number;
                };
                partner: {
                    type: string;
                    id: number;
                };
                picture: {
                    type: string;
                    id: number;
                };
                tac: {
                    type: string;
                    id: number;
                };
                web_version: {
                    type: string;
                    id: number;
                };
                board_owner_privileges: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                board_editor_privileges: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                board_viewer_privileges: {
                    type: string;
                    id: number;
                    options: {
                        default: number;
                    };
                };
                boards: {
                    rule: string;
                    type: string;
                    id: number;
                };
                recurly_signature: {
                    type: string;
                    id: number;
                };
                integrations: {
                    rule: string;
                    type: string;
                    id: number;
                };
                teams: {
                    rule: string;
                    type: string;
                    id: number;
                };
                cap: {
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                tags: {
                    rule: string;
                    type: string;
                    id: number;
                };
                group_telephony_domain: {
                    type: string;
                    id: number;
                };
                roles: {
                    rule: string;
                    type: string;
                    id: number;
                };
                tasks: {
                    rule: string;
                    type: string;
                    id: number;
                };
                app_config: {
                    type: string;
                    id: number;
                };
                routing_config: {
                    type: string;
                    id: number;
                };
                invitation_tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                redeem_urls: {
                    rule: string;
                    type: string;
                    id: number;
                };
                redeem_url_idx: {
                    type: string;
                    id: number;
                };
                board_properties: {
                    rule: string;
                    type: string;
                    id: number;
                };
                shared_content_library_group_id: {
                    type: string;
                    id: number;
                };
                total_members: {
                    type: string;
                    id: number;
                };
                total_local_members: {
                    type: string;
                    id: number;
                };
                total_content_libraries: {
                    type: string;
                    id: number;
                };
                total_managers: {
                    type: string;
                    id: number;
                };
                istemp: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PartnerType: {
            edition: string;
            values: {
                PARTNER_TYPE_RESELLER: number;
                PARTNER_TYPE_APP: number;
            };
        };
        PartnerCap: {
            edition: string;
            fields: {
                has_telephony_domain: {
                    type: string;
                    id: number;
                };
                has_sip: {
                    type: string;
                    id: number;
                };
                has_org_meet_usage: {
                    type: string;
                    id: number;
                };
            };
        };
        Partner: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                description: {
                    type: string;
                    id: number;
                };
                configuration: {
                    type: string;
                    id: number;
                };
                contact: {
                    type: string;
                    id: number;
                };
                partner_caps: {
                    type: string;
                    id: number;
                };
                allow_trial: {
                    type: string;
                    id: number;
                };
                max_trial_days: {
                    type: string;
                    id: number;
                };
                upgrade_info: {
                    type: string;
                    id: number;
                };
                shared_content_library_group_id: {
                    type: string;
                    id: number;
                };
                members: {
                    rule: string;
                    type: string;
                    id: number;
                };
                integrations: {
                    rule: string;
                    type: string;
                    id: number;
                };
                plan_codes: {
                    rule: string;
                    type: string;
                    id: number;
                };
                default_plan_code: {
                    type: string;
                    id: number;
                };
                default_plan_code_local: {
                    type: string;
                    id: number;
                };
                group_templates: {
                    rule: string;
                    type: string;
                    id: number;
                };
                webapps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                groups: {
                    rule: string;
                    type: string;
                    id: number;
                };
                partner_telephony_domains: {
                    rule: string;
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PartnerUser: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PartnerPlanCode: {
            edition: string;
            fields: {
                plan_code: {
                    type: string;
                    id: number;
                };
                plan_cap: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PartnerIntegration: {
            edition: string;
            fields: {
                idp_conf: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PartnerWebApp: {
            edition: string;
            fields: {
                webapp: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PartnerContact: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
            };
        };
        PartnerGroup: {
            edition: string;
            fields: {
                group: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        PartnerTelephonyDomain: {
            edition: string;
            fields: {
                telephony_domain: {
                    type: string;
                    id: number;
                };
                is_default: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemSamlService: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                service_providers: {
                    rule: string;
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SamlServiceProvider: {
            edition: string;
            fields: {
                sp_conf: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SamlIdentityProviderConfig: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                idpid: {
                    type: string;
                    id: number;
                };
                spid: {
                    type: string;
                    id: number;
                };
                authncontextclassref: {
                    type: string;
                    id: number;
                };
                targetparameter: {
                    type: string;
                    id: number;
                };
                idploginurl: {
                    type: string;
                    id: number;
                };
                assertionconsumerserviceurl: {
                    type: string;
                    id: number;
                };
                cert: {
                    type: string;
                    id: number;
                };
                nameidformat: {
                    type: string;
                    id: number;
                };
                nameid_has_unique_id: {
                    type: string;
                    id: number;
                };
                idpinitiated: {
                    type: string;
                    id: number;
                };
                postprofile: {
                    type: string;
                    id: number;
                };
                authnrequestsigned: {
                    type: string;
                    id: number;
                };
            };
        };
        SamlServiceProviderConfig: {
            edition: string;
            fields: {
                idpid: {
                    type: string;
                    id: number;
                };
                spid: {
                    type: string;
                    id: number;
                };
                authncontextclassref: {
                    type: string;
                    id: number;
                };
                targetparameter: {
                    type: string;
                    id: number;
                };
                sploginurl: {
                    type: string;
                    id: number;
                };
                nameidformat: {
                    type: string;
                    id: number;
                };
                encryption: {
                    type: string;
                    id: number;
                };
                algorithm: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                cert: {
                    type: string;
                    id: number;
                };
                key: {
                    type: string;
                    id: number;
                };
            };
        };
        UserWebApp: {
            edition: string;
            fields: {
                webapp: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WebAppUser: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                partner: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
            };
        };
        WebAppType: {
            edition: string;
            values: {
                WEBAPP_TYPE_SDK: number;
                WEBAPP_TYPE_SDK_PLUS_API: number;
                WEBAPP_TYPE_BOT: number;
                WEBAPP_TYPE_SUBSCRIPTION: number;
                WEBAPP_TYPE_EMBEDDED: number;
                WEBAPP_TYPE_CONNECTOR: number;
                WEBAPP_TYPE_INBOX_BOT: number;
                WEBAPP_TYPE_AI: number;
            };
        };
        WebApp: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                app_name: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                description_long: {
                    type: string;
                    id: number;
                };
                redirect_url: {
                    type: string;
                    id: number;
                };
                client_id: {
                    type: string;
                    id: number;
                };
                client_secret: {
                    type: string;
                    id: number;
                };
                grant_types: {
                    rule: string;
                    type: string;
                    id: number;
                };
                scopes: {
                    rule: string;
                    type: string;
                    id: number;
                };
                picture: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
                verification_token: {
                    type: string;
                    id: number;
                };
                verification_url: {
                    type: string;
                    id: number;
                };
                callback_url: {
                    type: string;
                    id: number;
                };
                template: {
                    type: string;
                    id: number;
                };
                template_richtext: {
                    type: string;
                    id: number;
                };
                instructions: {
                    type: string;
                    id: number;
                };
                note: {
                    type: string;
                    id: number;
                };
                desktop_home_url: {
                    type: string;
                    id: number;
                };
                mobile_home_url: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                nlp_type: {
                    type: string;
                    id: number;
                };
                apns_cert: {
                    type: string;
                    id: number;
                };
                apns_private_key: {
                    type: string;
                    id: number;
                };
                apns_password: {
                    type: string;
                    id: number;
                };
                is_apns_cert_expired: {
                    type: string;
                    id: number;
                };
                apns_development: {
                    type: string;
                    id: number;
                };
                voip_cert: {
                    type: string;
                    id: number;
                };
                voip_private_key: {
                    type: string;
                    id: number;
                };
                voip_password: {
                    type: string;
                    id: number;
                };
                is_voip_cert_expired: {
                    type: string;
                    id: number;
                };
                gcm_api_key: {
                    type: string;
                    id: number;
                };
                optional_gcm_url: {
                    type: string;
                    id: number;
                };
                has_badge: {
                    type: string;
                    id: number;
                };
                ios_app_id: {
                    type: string;
                    id: number;
                };
                auth_key_id: {
                    type: string;
                    id: number;
                };
                auth_key: {
                    type: string;
                    id: number;
                };
                team_id: {
                    type: string;
                    id: number;
                };
                bundle_id: {
                    type: string;
                    id: number;
                };
                apple_oauth_bundle_id: {
                    type: string;
                    id: number;
                };
                android_app_namespace: {
                    type: string;
                    id: number;
                };
                android_app_pkg_name: {
                    type: string;
                    id: number;
                };
                android_app_fingerprints: {
                    rule: string;
                    type: string;
                    id: number;
                };
                google_oauth_client_id: {
                    type: string;
                    id: number;
                };
                owner: {
                    type: string;
                    id: number;
                };
                sound_default: {
                    type: string;
                    id: number;
                };
                sound_meeting_call: {
                    type: string;
                    id: number;
                };
                tags: {
                    rule: string;
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                vendors: {
                    rule: string;
                    type: string;
                    id: number;
                };
                is_universal: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        NotificationVendor: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                app_id: {
                    type: string;
                    id: number;
                };
                app_secret: {
                    type: string;
                    id: number;
                };
                authenticate_url: {
                    type: string;
                    id: number;
                };
                push_url: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardResourceType: {
            edition: string;
            values: {
                BOARD_RESOURCE_NONE: number;
                BOARD_RESOURCE_THUMBNAIL: number;
                BOARD_RESOURCE_BACKGROUND: number;
                BOARD_RESOURCE_VECTOR: number;
                BOARD_RESOURCE_EMBEDDED: number;
                BOARD_RESOURCE_VECTOR_THUMBNAIL: number;
                BOARD_RESOURCE_BOARD_AS_PDF: number;
                BOARD_RESOURCE_BOARD_AS_PPT: number;
                BOARD_RESOURCE_RECORDING: number;
                BOARD_RESOURCE_SESSION_AS_VIDEO: number;
                BOARD_RESOURCE_COVER: number;
                BOARD_RESOURCE_BANNER: number;
                BOARD_RESOURCE_AUDIO_RECORDING: number;
                BOARD_RESOURCE_AVATAR: number;
                BOARD_RESOURCE_SIGNATURE_AS_PDF: number;
                BOARD_RESOURCE_SESSION_AUDIO_SPEAKER: number;
                BOARD_RESOURCE_SESSION_MEET_CHAT: number;
                BOARD_RESOURCE_TRANSACTION_AS_PDF: number;
                BOARD_RESOURCE_TRANSACTION_AS_CSV: number;
                BOARD_RESOURCE_SESSION_TRANSCRIPTION: number;
                BOARD_RESOURCE_SESSION_TRANSCRIPTION_VTT: number;
                BOARD_RESOURCE_SESSION_SUMMARY: number;
            };
        };
        BoardResourceStatus: {
            edition: string;
            values: {
                BOARD_RESOURCE_STATUS_NONE: number;
                BOARD_RESOURCE_STATUS_QUEUED: number;
                BOARD_RESOURCE_STATUS_CONVERTING: number;
                BOARD_RESOURCE_STATUS_CONVERTED: number;
                BOARD_RESOURCE_STATUS_CONVERT_FAILED: number;
                BOARD_RESOURCE_STATUS_KEEP_UNCONVERTED: number;
                BOARD_RESOURCE_STATUS_TOO_MANY_PAGES: number;
                BOARD_RESOURCE_STATUS_TOO_LARGE: number;
                BOARD_RESOURCE_STATUS_INVALID_PASSWORD: number;
            };
        };
        BoardResource: {
            edition: string;
            fields: {
                part: {
                    type: string;
                    id: number;
                };
                origin: {
                    type: string;
                    id: number;
                };
                use_origin_grouping: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                path: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                content_type: {
                    type: string;
                    id: number;
                };
                content_length: {
                    type: string;
                    id: number;
                };
                media_length: {
                    type: string;
                    id: number;
                };
                is_password_protected: {
                    type: string;
                    id: number;
                };
                password: {
                    type: string;
                    id: number;
                };
                sha256_hash: {
                    type: string;
                    id: number;
                };
                hash: {
                    type: string;
                    id: number;
                };
                rotate: {
                    type: string;
                    id: number;
                };
                width: {
                    type: string;
                    id: number;
                };
                height: {
                    type: string;
                    id: number;
                };
                OBSOLETE_creator_sequence: {
                    type: string;
                    id: number;
                };
                OBSOLETE_creator: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                upload_sequence: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                converted_pages: {
                    type: string;
                    id: number;
                };
                total_pages: {
                    type: string;
                    id: number;
                };
                max_pages: {
                    type: string;
                    id: number;
                };
                pages: {
                    rule: string;
                    type: string;
                    id: number;
                };
                session: {
                    type: string;
                    id: number;
                };
                email_subject: {
                    type: string;
                    id: number;
                };
                from_email: {
                    type: string;
                    id: number;
                };
                from_name: {
                    type: string;
                    id: number;
                };
                is_email_empty: {
                    type: string;
                    id: number;
                };
                file: {
                    type: string;
                    id: number;
                };
                destination_board: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                original_resource_sequence: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardTagName: {
            edition: string;
            fields: {
                meet_agenda: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                meet_document_board_id: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
            };
        };
        BoardTag: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                string_value: {
                    type: string;
                    id: number;
                };
                uint64_value: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardUserStatus: {
            edition: string;
            values: {
                BOARD_MEMBER: number;
                BOARD_INVITED: number;
                BOARD_MEMBER_REQUESTED: number;
                BOARD_VISITED: number;
            };
        };
        BoardAccessType: {
            edition: string;
            values: {
                BOARD_NO_ACCESS: number;
                BOARD_UPLOAD_ONLY: number;
                BOARD_READ: number;
                BOARD_READ_WRITE: number;
                BOARD_OWNER: number;
            };
        };
        BoardRoutingStatus: {
            edition: string;
            values: {
                ROUTING_STATUS_INVALID: number;
                ROUTING_STATUS_OPEN: number;
                ROUTING_STATUS_OPEN_NO_TIMEOUT: number;
                ROUTING_STATUS_IN_PROGRESS: number;
                ROUTING_STATUS_AGENT_COMPLETE: number;
                ROUTING_STATUS_CLOSE: number;
                ROUTING_STATUS_CLOSE_TIMEOUT: number;
                ROUTING_STATUS_OFFICE_CLOSE: number;
                ROUTING_STATUS_BOT_IN_PROGRESS: number;
            };
        };
        BoardUserAOSM: {
            edition: string;
            fields: {
                timestamp: {
                    type: string;
                    id: number;
                };
                reply: {
                    type: string;
                    id: number;
                };
            };
        };
        RequestingUserStatus: {
            edition: string;
            values: {
                REQUESTING_USER_STATUS_INVALID: number;
                REQUESTING_USER_STATUS_PENDING: number;
                REQUESTING_USER_STATUS_APPROVED: number;
                REQUESTING_USER_STATUS_DENIED: number;
                REQUESTING_USER_STATUS_JOINED: number;
            };
        };
        BoardUser: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                group: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                is_notification_off: {
                    type: string;
                    id: number;
                };
                push_notification_level: {
                    type: string;
                    id: number;
                };
                type_indication_timestamp: {
                    type: string;
                    id: number;
                };
                is_alternative_host: {
                    type: string;
                    id: number;
                };
                is_owner_delegate: {
                    type: string;
                    id: number;
                };
                accessed_time: {
                    type: string;
                    id: number;
                };
                first_unread_feed_timestamp: {
                    type: string;
                    id: number;
                };
                invited_time: {
                    type: string;
                    id: number;
                };
                is_invited_in_session: {
                    type: string;
                    id: number;
                };
                action: {
                    type: string;
                    id: number;
                };
                is_from_team: {
                    type: string;
                    id: number;
                };
                teams: {
                    rule: string;
                    type: string;
                    id: number;
                };
                participant_number: {
                    type: string;
                    id: number;
                };
                requesting_user_status: {
                    type: string;
                    id: number;
                };
                responder: {
                    type: string;
                    id: number;
                };
                invite_msg: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardSession: {
            edition: string;
            fields: {
                session: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                previous_session: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                is_not_recurring: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        RichTextFormat: {
            edition: string;
            values: {
                TEXT_FORMAT_BBCODE: number;
                TEXT_FORMAT_CARD: number;
            };
        };
        BoardComment: {
            edition: string;
            fields: {
                OBSOLETE_creator_sequence: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                roster: {
                    type: string;
                    id: number;
                };
                text: {
                    type: string;
                    id: number;
                };
                rich_text: {
                    type: string;
                    id: number;
                };
                rich_text_format: {
                    type: string;
                    id: number;
                };
                x: {
                    type: string;
                    id: number;
                };
                y: {
                    type: string;
                    id: number;
                };
                position_comment_index: {
                    type: string;
                    id: number;
                };
                resource: {
                    type: string;
                    id: number;
                };
                resource_view_token: {
                    type: string;
                    id: number;
                };
                resource_path: {
                    type: string;
                    id: number;
                };
                resource_length: {
                    type: string;
                    id: number;
                };
                url_preview: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                is_modified: {
                    type: string;
                    id: number;
                };
                is_position_comment: {
                    type: string;
                    id: number;
                };
                original_resource_sequence: {
                    type: string;
                    id: number;
                };
                original_comment: {
                    type: string;
                    id: number;
                };
                original_page_group: {
                    type: string;
                    id: number;
                };
                original_session: {
                    type: string;
                    id: number;
                };
                original_signature: {
                    type: string;
                    id: number;
                };
                original_transaction: {
                    type: string;
                    id: number;
                };
                original_reference_link: {
                    type: string;
                    id: number;
                };
                custom_info: {
                    type: string;
                    id: number;
                };
                social_custom_info: {
                    type: string;
                    id: number;
                };
                custom_data: {
                    type: string;
                    id: number;
                };
                pin: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                revisions: {
                    rule: string;
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardFolderType: {
            edition: string;
            values: {
                FOLDER_TYPE_NONE: number;
                FOLDER_TYPE_EMAIL: number;
                FOLDER_TYPE_TRANSACTION: number;
            };
        };
        BoardFolder: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                is_recycled: {
                    type: string;
                    id: number;
                };
                folder_type: {
                    type: string;
                    id: number;
                };
                folders: {
                    rule: string;
                    type: string;
                    id: number;
                };
                files: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardPageGroup: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                original: {
                    type: string;
                    id: number;
                };
                original_signature: {
                    type: string;
                    id: number;
                };
                is_recycled: {
                    type: string;
                    id: number;
                };
                reference_link: {
                    type: string;
                    id: number;
                };
                thumbnail: {
                    type: string;
                    id: number;
                };
                first_page: {
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                total_changes: {
                    type: string;
                    id: number;
                };
                custom_info: {
                    type: string;
                    id: number;
                };
                social_custom_info: {
                    type: string;
                    id: number;
                };
                total_used_count: {
                    type: string;
                    id: number;
                };
                last_used_timestamp: {
                    type: string;
                    id: number;
                };
                pin: {
                    type: string;
                    id: number;
                };
                display_name: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                original_created_time: {
                    type: string;
                    id: number;
                };
                last_modified_time: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardViewToken: {
            edition: string;
            fields: {
                token: {
                    type: string;
                    id: number;
                };
                expire_timestamp: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                actor: {
                    type: string;
                    id: number;
                };
                actor_file_as: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                pages: {
                    rule: string;
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                page_groups: {
                    rule: string;
                    type: string;
                    id: number;
                };
                folders: {
                    rule: string;
                    type: string;
                    id: number;
                };
                signatures: {
                    rule: string;
                    type: string;
                    id: number;
                };
                is_invitation_token: {
                    type: string;
                    id: number;
                };
                note: {
                    type: string;
                    id: number;
                };
                is_outgoing: {
                    type: string;
                    id: number;
                };
                disabled: {
                    type: string;
                    id: number;
                };
                users: {
                    rule: string;
                    type: string;
                    id: number;
                };
                variables: {
                    rule: string;
                    type: string;
                    id: number;
                };
                is_share_token: {
                    type: string;
                    id: number;
                };
                code: {
                    type: string;
                    id: number;
                };
                auto_approve: {
                    type: string;
                    id: number;
                };
                member_only: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardPageElement: {
            edition: string;
            fields: {
                svg_tag: {
                    type: string;
                    id: number;
                };
                highlight: {
                    type: string;
                    id: number;
                };
                readonly: {
                    type: string;
                    id: number;
                };
                resource: {
                    type: string;
                    id: number;
                };
                resource_path: {
                    type: string;
                    id: number;
                };
                resource_name: {
                    type: string;
                    id: number;
                };
                creator_sequence: {
                    type: string;
                    id: number;
                };
                OBSOLETE_user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                tags: {
                    rule: string;
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardPageType: {
            edition: string;
            values: {
                PAGE_TYPE_WHITEBOARD: number;
                PAGE_TYPE_NOT_SUPPORTED: number;
                PAGE_TYPE_IMAGE: number;
                PAGE_TYPE_WEB: number;
                PAGE_TYPE_VIDEO: number;
                PAGE_TYPE_AUDIO: number;
                PAGE_TYPE_PDF: number;
                PAGE_TYPE_URL: number;
                PAGE_TYPE_NOTE: number;
                PAGE_TYPE_DESKTOPSHARE: number;
                PAGE_TYPE_GEO: number;
                PAGE_TYPE_ANY: number;
            };
        };
        FormFieldType: {
            edition: string;
            values: {
                FORM_FIELD_TYPE_INVLIAD: number;
                FORM_FIELD_TYPE_PUSH_BUTTON: number;
                FORM_FIELD_TYPE_RADIO_BUTTON: number;
                FORM_FIELD_TYPE_CHECKBOX: number;
                FORM_FIELD_TYPE_TEXT: number;
                FORM_FIELD_TYPE_CHOICE: number;
                FORM_FIELD_TYPE_SIGNATURE: number;
            };
        };
        BoardPageFormField: {
            edition: string;
            fields: {
                type: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                x: {
                    type: string;
                    id: number;
                };
                y: {
                    type: string;
                    id: number;
                };
                width: {
                    type: string;
                    id: number;
                };
                height: {
                    type: string;
                    id: number;
                };
                flags: {
                    type: string;
                    id: number;
                };
                is_multiple_line: {
                    type: string;
                    id: number;
                };
                is_password: {
                    type: string;
                    id: number;
                };
                default_value: {
                    type: string;
                    id: number;
                };
                is_multiple_select: {
                    type: string;
                    id: number;
                };
                choices: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardPage: {
            edition: string;
            fields: {
                creator_sequence: {
                    type: string;
                    id: number;
                };
                OBSOLETE_user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                svg_begining_tag: {
                    type: string;
                    id: number;
                };
                contents: {
                    rule: string;
                    type: string;
                    id: number;
                };
                svg_ending_tag: {
                    type: string;
                    id: number;
                };
                original_resource_name: {
                    type: string;
                    id: number;
                };
                original_page_number: {
                    type: string;
                    id: number;
                };
                original_resource_upload_sequence: {
                    type: string;
                    id: number;
                };
                original_resource_sequence: {
                    type: string;
                    id: number;
                };
                inherited_original_resource_sequence: {
                    type: string;
                    id: number;
                };
                is_original_resource_from_page: {
                    type: string;
                    id: number;
                };
                update_if_revision_match: {
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                longitude: {
                    type: string;
                    id: number;
                };
                latitude: {
                    type: string;
                    id: number;
                };
                tags: {
                    rule: string;
                    type: string;
                    id: number;
                };
                page_number: {
                    type: string;
                    id: number;
                };
                page_group: {
                    type: string;
                    id: number;
                };
                file: {
                    type: string;
                    id: number;
                };
                page_type: {
                    type: string;
                    id: number;
                };
                original: {
                    type: string;
                    id: number;
                };
                vector: {
                    type: string;
                    id: number;
                };
                background: {
                    type: string;
                    id: number;
                };
                thumbnail: {
                    type: string;
                    id: number;
                };
                text: {
                    type: string;
                    id: number;
                };
                thumbnail_view_token: {
                    type: string;
                    id: number;
                };
                media_length: {
                    type: string;
                    id: number;
                };
                original_path: {
                    type: string;
                    id: number;
                };
                vector_path: {
                    type: string;
                    id: number;
                };
                background_path: {
                    type: string;
                    id: number;
                };
                thumbnail_path: {
                    type: string;
                    id: number;
                };
                width: {
                    type: string;
                    id: number;
                };
                height: {
                    type: string;
                    id: number;
                };
                url: {
                    type: string;
                    id: number;
                };
                rotate: {
                    type: string;
                    id: number;
                };
                comments: {
                    rule: string;
                    type: string;
                    id: number;
                };
                total_comments: {
                    type: string;
                    id: number;
                };
                total_position_comments: {
                    type: string;
                    id: number;
                };
                original_session_key: {
                    type: string;
                    id: number;
                };
                editor: {
                    type: string;
                    id: number;
                };
                editor_actor: {
                    type: string;
                    id: number;
                };
                editor_time: {
                    type: string;
                    id: number;
                };
                editor_type: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                description: {
                    type: string;
                    id: number;
                };
                vector_thumbnail: {
                    type: string;
                    id: number;
                };
                vector_thumbnail_path: {
                    type: string;
                    id: number;
                };
                form_fields: {
                    rule: string;
                    type: string;
                    id: number;
                };
                card: {
                    type: string;
                    id: number;
                };
                ddrs: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardEditorType: {
            edition: string;
            values: {
                EDITOR_TYPE_ALL: number;
                EDITOR_TYPE_INTERNAL_ONLY: number;
                EDITOR_TYPE_ASSIGNEE_ONLY: number;
            };
        };
        BoardReferenceLink: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardReferenceType: {
            edition: string;
            values: {
                REFERENCE_TYPE_ATTACHEMENT: number;
                REFERENCE_TYPE_SUPPORT_FILE: number;
                REFERENCE_TYPE_FILE_REPLY: number;
                REFERENCE_TYPE_PDF_FORM_FILE: number;
            };
        };
        BoardReference: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator_sequence: {
                    type: string;
                    id: number;
                };
                OBSOLETE_user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                custom_data: {
                    type: string;
                    id: number;
                };
                keep_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardReminder: {
            edition: string;
            fields: {
                creator_sequence: {
                    type: string;
                    id: number;
                };
                OBSOLETE_user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                reminder_time: {
                    type: string;
                    id: number;
                };
                reminder_interval: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        DueTimeFrameType: {
            edition: string;
            values: {
                DUE_TIME_FRAME_DAYS: number;
                DUE_TIME_FRAME_WEEKS: number;
            };
        };
        BoardTodo: {
            edition: string;
            fields: {
                creator_sequence: {
                    type: string;
                    id: number;
                };
                OBSOLETE_user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                creator: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                note: {
                    type: string;
                    id: number;
                };
                is_marked: {
                    type: string;
                    id: number;
                };
                assignee_sequence: {
                    type: string;
                    id: number;
                };
                assignee: {
                    type: string;
                    id: number;
                };
                due_date: {
                    type: string;
                    id: number;
                };
                due_in_timeframe: {
                    type: string;
                    id: number;
                };
                exclude_weekends: {
                    type: string;
                    id: number;
                };
                is_completed: {
                    type: string;
                    id: number;
                };
                detail_status: {
                    type: string;
                    id: number;
                };
                is_template: {
                    type: string;
                    id: number;
                };
                template_name: {
                    type: string;
                    id: number;
                };
                template_description: {
                    type: string;
                    id: number;
                };
                original_client_uuid: {
                    type: string;
                    id: number;
                };
                total_used_count: {
                    type: string;
                    id: number;
                };
                last_used_timestamp: {
                    type: string;
                    id: number;
                };
                last_modified_time: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                comments: {
                    rule: string;
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                references: {
                    rule: string;
                    type: string;
                    id: number;
                };
                reminders: {
                    rule: string;
                    type: string;
                    id: number;
                };
                editable_editor_type: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                completable_editor_type: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                pin: {
                    type: string;
                    id: number;
                };
                update_if_revision_match: {
                    type: string;
                    id: number;
                };
                workflow: {
                    type: string;
                    id: number;
                };
                step: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardActor: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                group: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                roster: {
                    type: string;
                    id: number;
                };
                is_null: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardCallStatus: {
            edition: string;
            values: {
                BOARD_CALL_STATUS_INVALID: number;
                BOARD_CALL_STATUS_ENDED: number;
                BOARD_CALL_STATUS_CANCELLED: number;
            };
        };
        BoardCallLog: {
            edition: string;
            fields: {
                from: {
                    type: string;
                    id: number;
                };
                to: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                call_status: {
                    type: string;
                    id: number;
                };
                creator: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardSignatureStatus: {
            edition: string;
            values: {
                SIGNATURE_STATUS_INVALID: number;
                SIGNATURE_STATUS_PREPARING: number;
                SIGNATURE_STATUS_EDITING: number;
                SIGNATURE_STATUS_IN_PROGRESS: number;
                SIGNATURE_STATUS_COMPLETED: number;
                SIGNATURE_STATUS_DECLINED: number;
            };
        };
        DetailStatusCode: {
            edition: string;
            values: {
                DETAIL_STATUS_NONE: number;
                DETAIL_STATUS_MARK_AS_COMPLETED: number;
            };
        };
        BoardSigneeStatus: {
            edition: string;
            values: {
                SIGNEE_STATUS_NONE: number;
                SIGNEE_STATUS_SKIPPED: number;
            };
        };
        BoardSignee: {
            edition: string;
            fields: {
                elements: {
                    rule: string;
                    type: string;
                    id: number;
                };
                submitted_elements: {
                    rule: string;
                    type: string;
                    id: number;
                };
                actor: {
                    type: string;
                    id: number;
                };
                is_submitted: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                msg: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                requested_time: {
                    type: string;
                    id: number;
                };
                viewed_time: {
                    type: string;
                    id: number;
                };
                submitted_time: {
                    type: string;
                    id: number;
                };
                submitted_ip: {
                    type: string;
                    id: number;
                };
                submitted_via: {
                    type: string;
                    id: number;
                };
                signature: {
                    type: string;
                    id: number;
                };
                initials_text: {
                    type: string;
                    id: number;
                };
                signature_style: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardSignature: {
            edition: string;
            fields: {
                creator: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                detail_status: {
                    type: string;
                    id: number;
                };
                signees: {
                    rule: string;
                    type: string;
                    id: number;
                };
                enable_preparation: {
                    type: string;
                    id: number;
                };
                editor: {
                    type: string;
                    id: number;
                };
                doc_id: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                original_name: {
                    type: string;
                    id: number;
                };
                is_from_pdf_form: {
                    type: string;
                    id: number;
                };
                pages: {
                    rule: string;
                    type: string;
                    id: number;
                };
                original: {
                    type: string;
                    id: number;
                };
                coc: {
                    type: string;
                    id: number;
                };
                original_with_coc: {
                    type: string;
                    id: number;
                };
                file: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                is_template: {
                    type: string;
                    id: number;
                };
                template_name: {
                    type: string;
                    id: number;
                };
                template_description: {
                    type: string;
                    id: number;
                };
                original_client_uuid: {
                    type: string;
                    id: number;
                };
                sign_by_order: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                started_time: {
                    type: string;
                    id: number;
                };
                started_by_ip: {
                    type: string;
                    id: number;
                };
                started_via: {
                    type: string;
                    id: number;
                };
                ended_time: {
                    type: string;
                    id: number;
                };
                due_date: {
                    type: string;
                    id: number;
                };
                due_in_timeframe: {
                    type: string;
                    id: number;
                };
                exclude_weekends: {
                    type: string;
                    id: number;
                };
                references: {
                    rule: string;
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                original_resource_sequence: {
                    type: string;
                    id: number;
                };
                pin: {
                    type: string;
                    id: number;
                };
                total_used_count: {
                    type: string;
                    id: number;
                };
                last_used_timestamp: {
                    type: string;
                    id: number;
                };
                last_modified_time: {
                    type: string;
                    id: number;
                };
                workflow: {
                    type: string;
                    id: number;
                };
                step: {
                    type: string;
                    id: number;
                };
                is_workflow_source: {
                    type: string;
                    id: number;
                };
                ddrs: {
                    rule: string;
                    type: string;
                    id: number;
                };
                has_custom_folder: {
                    type: string;
                    id: number;
                };
                custom_folder_name: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardUserActivity: {
            edition: string;
            fields: {
                actor: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        TransactionActionStyle: {
            edition: string;
            values: {
                ACTION_STYLE_BUTTON: number;
                ACTION_STYLE_CHECKBOX: number;
            };
        };
        TransactionStepType: {
            edition: string;
            values: {
                STEP_TYPE_GENERIC: number;
                STEP_TYPE_DOCUSIGN_CC: number;
                STEP_TYPE_REVIEWER: number;
            };
        };
        TransactionStepStatus: {
            edition: string;
            values: {
                STEP_STATUS_INITIAL: number;
                STEP_STATUS_PENDING: number;
                STEP_STATUS_COMPLETED: number;
                STEP_STATUS_CANCELED: number;
                STEP_STATUS_SKIPPED: number;
                STEP_STATUS_REOPENED: number;
            };
        };
        TransactionStatus: {
            edition: string;
            values: {
                TRANSACTION_STATUS_EDITING: number;
                TRANSACTION_STATUS_ACTIVE: number;
                TRANSACTION_STATUS_INACTIVE: number;
                TRANSACTION_STATUS_COMPLETED: number;
                TRANSACTION_STATUS_CANCELED: number;
                TRANSACTION_STATUS_EXPIRED: number;
            };
        };
        TransactionActionLog: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                click_btn_id: {
                    type: string;
                    id: number;
                };
                click_btn_timestamp: {
                    type: string;
                    id: number;
                };
                click_btn_from_ip: {
                    type: string;
                    id: number;
                };
                custom_action: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        TransactionElement: {
            edition: string;
            fields: {
                string_value: {
                    type: string;
                    id: number;
                };
                creator: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        TransactionStep: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                assignee: {
                    type: string;
                    id: number;
                };
                action_style: {
                    type: string;
                    id: number;
                };
                actions: {
                    type: string;
                    id: number;
                };
                action_logs: {
                    rule: string;
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                step_group: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                viewed_time: {
                    type: string;
                    id: number;
                };
                custom_data: {
                    type: string;
                    id: number;
                };
                contents: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        TransactionStepGroup: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                completion_type: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        StepGroupCompletionType: {
            edition: string;
            values: {
                STEP_GROUP_COMPLETION_TYPE_ALL: number;
                STEP_GROUP_COMPLETION_TYPE_ONE: number;
                STEP_GROUP_COMPLETION_TYPE_MAJORITY: number;
            };
        };
        TransactionType: {
            edition: string;
            values: {
                TRANSACTION_TYPE_GENERIC: number;
                TRANSACTION_TYPE_APPROVAL: number;
                TRANSACTION_TYPE_ACKNOWLEDGE: number;
                TRANSACTION_TYPE_FILE_REQUEST: number;
                TRANSACTION_TYPE_MEET_REQUEST: number;
                TRANSACTION_TYPE_FORM_REQUEST: number;
                TRANSACTION_TYPE_TIME_BOOKING: number;
                TRANSACTION_TYPE_PDF_FORM: number;
                TRANSACTION_TYPE_DOCUSIGN: number;
                TRANSACTION_TYPE_WEBHOOK: number;
                TRANSACTION_TYPE_LAUNCH_WEB_APP: number;
                TRANSACTION_TYPE_INTEGRATION: number;
                TRANSACTION_TYPE_TODO: number;
                TRANSACTION_TYPE_DECISION: number;
                TRANSACTION_TYPE_AWAIT: number;
            };
        };
        BoardTransaction: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                title: {
                    type: string;
                    id: number;
                };
                sub_title: {
                    type: string;
                    id: number;
                };
                content: {
                    type: string;
                    id: number;
                };
                content_format: {
                    type: string;
                    id: number;
                };
                display_status: {
                    type: string;
                    id: number;
                };
                is_active: {
                    type: string;
                    id: number;
                };
                expiration_date: {
                    type: string;
                    id: number;
                };
                is_expired: {
                    type: string;
                    id: number;
                };
                due_in_timeframe: {
                    type: string;
                    id: number;
                };
                exclude_weekends: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                detail_status: {
                    type: string;
                    id: number;
                };
                callback_url: {
                    type: string;
                    id: number;
                };
                is_template: {
                    type: string;
                    id: number;
                };
                template_name: {
                    type: string;
                    id: number;
                };
                template_description: {
                    type: string;
                    id: number;
                };
                original_client_uuid: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                creator: {
                    type: string;
                    id: number;
                };
                enable_preparation: {
                    type: string;
                    id: number;
                };
                editor: {
                    type: string;
                    id: number;
                };
                enable_decline: {
                    type: string;
                    id: number;
                };
                steps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                step_groups: {
                    rule: string;
                    type: string;
                    id: number;
                };
                step_timeout: {
                    type: string;
                    id: number;
                };
                references: {
                    rule: string;
                    type: string;
                    id: number;
                };
                view_tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                card: {
                    type: string;
                    id: number;
                };
                card_description: {
                    type: string;
                    id: number;
                };
                custom_data: {
                    type: string;
                    id: number;
                };
                sub_type: {
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                reminders: {
                    rule: string;
                    type: string;
                    id: number;
                };
                custom_result: {
                    type: string;
                    id: number;
                };
                segments: {
                    rule: string;
                    type: string;
                    id: number;
                };
                pin: {
                    type: string;
                    id: number;
                };
                total_used_count: {
                    type: string;
                    id: number;
                };
                last_used_timestamp: {
                    type: string;
                    id: number;
                };
                last_modified_time: {
                    type: string;
                    id: number;
                };
                workflow: {
                    type: string;
                    id: number;
                };
                step: {
                    type: string;
                    id: number;
                };
                is_workflow_source: {
                    type: string;
                    id: number;
                };
                original: {
                    type: string;
                    id: number;
                };
                original_masked: {
                    type: string;
                    id: number;
                };
                original_csv: {
                    type: string;
                    id: number;
                };
                original_csv_masked: {
                    type: string;
                    id: number;
                };
                has_custom_folder: {
                    type: string;
                    id: number;
                };
                custom_folder_name: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SocialType: {
            edition: string;
            values: {
                SOCIAL_TYPE_INVALID: number;
                SOCIAL_TYPE_WECHAT: number;
                SOCIAL_TYPE_LINE: number;
                SOCIAL_TYPE_WHATSAPP: number;
            };
        };
        RSVPStatus: {
            edition: string;
            values: {
                RSVP_NEEDS_ACTION: number;
                RSVP_ACCEPTED: number;
                RSVP_DECLINED: number;
                RSVP_TENTATIVE: number;
            };
        };
        RSVPReply: {
            edition: string;
            fields: {
                from_email: {
                    type: string;
                    id: number;
                };
                original: {
                    type: string;
                    id: number;
                };
                timezone: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                dtstart: {
                    type: string;
                    id: number;
                };
                dtend: {
                    type: string;
                    id: number;
                };
                rrule: {
                    type: string;
                    id: number;
                };
                partstat: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardUserRSVP: {
            edition: string;
            fields: {
                actor: {
                    type: string;
                    id: number;
                };
                replies: {
                    rule: string;
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardPin: {
            edition: string;
            fields: {
                actor: {
                    type: string;
                    id: number;
                };
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardDataReference: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                value: {
                    type: string;
                    id: number;
                };
                label: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowType: {
            edition: string;
            values: {
                WORKFLOW_TYPE_DEFAULT: number;
                WORKFLOW_TYPE_FLEXIBLE: number;
            };
        };
        WorkflowStatus: {
            edition: string;
            values: {
                WORKFLOW_STATUS_INITIAL: number;
                WORKFLOW_STATUS_RUNNING: number;
                WORKFLOW_STATUS_COMPLETED: number;
                WORKFLOW_STATUS_CANCELED: number;
                WORKFLOW_STATUS_FAULTED: number;
            };
        };
        WorkflowVarParameter: {
            edition: string;
            values: {
                VAR_PARAM_USER_ID_TO_GROUP_ID: number;
                VAR_PARAM_QUERY_REPEATED: number;
                VAR_PARAM_EXPEND_ARRAY: number;
                VAR_PARAM_FORMAT: number;
            };
        };
        WorkflowVarParam: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                string_value: {
                    type: string;
                    id: number;
                };
                uint64_value: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowVarType: {
            edition: string;
            values: {
                VAR_TYPE_DEFAULT: number;
                VAR_TYPE_ALL_FILES: number;
                VAR_TYPE_FORM_PDF: number;
                VAR_TYPE_FORM_CSV: number;
                VAR_TYPE_WORKSPACE_VARIABLE: number;
                VAR_TYPE_WORKSPACE_EMAIL: number;
                VAR_TYPE_WORKSPACE_URL: number;
            };
        };
        WorkflowVar: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                string_value: {
                    type: string;
                    id: number;
                };
                default_value: {
                    type: string;
                    id: number;
                };
                default_actor: {
                    type: string;
                    id: number;
                };
                label: {
                    type: string;
                    id: number;
                };
                refer_to: {
                    type: string;
                    id: number;
                };
                resolved: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                custom_data: {
                    type: string;
                    id: number;
                };
                params: {
                    rule: string;
                    type: string;
                    id: number;
                };
                var_type: {
                    type: string;
                    id: number;
                };
                step_uuid: {
                    type: string;
                    id: number;
                };
                timeout: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowStepType: {
            edition: string;
            values: {
                WORKFLOW_STEP_TYPE_INVALID: number;
                WORKFLOW_STEP_TYPE_MILESTONE: number;
                WORKFLOW_STEP_TYPE_AB: number;
                WORKFLOW_STEP_TYPE_AUTOMATION: number;
                WORKFLOW_STEP_TYPE_CB: number;
                WORKFLOW_STEP_TYPE_SHADOW_FLOW: number;
                WORKFLOW_STEP_TYPE_SEND_FILE: number;
                WORKFLOW_STEP_TYPE_TRANSACTION: number;
                WORKFLOW_STEP_TYPE_FORM_REQUEST: number;
                WORKFLOW_STEP_TYPE_FILE_REQUEST: number;
                WORKFLOW_STEP_TYPE_MEET_REQUEST: number;
                WORKFLOW_STEP_TYPE_APPROVAL: number;
                WORKFLOW_STEP_TYPE_ACKNOWLEDGE: number;
                WORKFLOW_STEP_TYPE_SIGNATURE: number;
                WORKFLOW_STEP_TYPE_TODO: number;
                WORKFLOW_STEP_TYPE_TIME_BOOKING: number;
                WORKFLOW_STEP_TYPE_DOCUSIGN: number;
                WORKFLOW_STEP_TYPE_WEBHOOK: number;
                WORKFLOW_STEP_TYPE_LAUNCH_WEB_APP: number;
                WORKFLOW_STEP_TYPE_INTEGRATION: number;
                WORKFLOW_STEP_TYPE_TODO_TRANSACTION: number;
                WORKFLOW_STEP_TYPE_DECISION: number;
                WORKFLOW_STEP_TYPE_AWAIT: number;
                WORKFLOW_STEP_TYPE_PDF_FORM: number;
                WORKFLOW_STEP_TYPE_SHADOW_ACTION: number;
            };
        };
        WorkflowStepStatus: {
            edition: string;
            values: {
                WORKFLOW_STEP_STATUS_INITIAL: number;
                WORKFLOW_STEP_STATUS_PREPARING: number;
                WORKFLOW_STEP_STATUS_READY: number;
                WORKFLOW_STEP_STATUS_STARTED: number;
                WORKFLOW_STEP_STATUS_COMPLETED: number;
                WORKFLOW_STEP_STATUS_CANCELED: number;
                WORKFLOW_STEP_STATUS_FAULTED: number;
                WORKFLOW_STEP_STATUS_SKIPPED: number;
                WORKFLOW_STEP_STATUS_PRECONDITION_NOT_MET: number;
            };
        };
        WorkflowOutgoing: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                feed: {
                    type: string;
                    id: number;
                };
                payload: {
                    type: string;
                    id: number;
                };
                webhooks: {
                    type: string;
                    id: number;
                };
                triggers: {
                    type: string;
                    id: number;
                };
                integrations: {
                    type: string;
                    id: number;
                };
                integrations_ext: {
                    type: string;
                    id: number;
                };
                queue_type: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowOutgoingQueueType: {
            edition: string;
            values: {
                QUEUE_TYPE_GROUP: number;
                QUEUE_TYPE_BOARD: number;
            };
        };
        WorkflowConditionCategory: {
            edition: string;
            values: {
                CONDITION_DEFAULT: number;
                CONDITION_EXECUTION_ORDER: number;
                CONDITION_EXECUTION_BRANCH: number;
                CONDITION_EXECUTION_MIXED: number;
            };
        };
        WorkflowCondition: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                expression: {
                    type: string;
                    id: number;
                };
                result: {
                    type: string;
                    id: number;
                };
                original_result: {
                    type: string;
                    id: number;
                };
                resolved: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
                conditional_step: {
                    type: string;
                    id: number;
                };
                waiting_steps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                waiting_milestones: {
                    rule: string;
                    type: string;
                    id: number;
                };
                waiting_conditional_steps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                variables: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowActionType: {
            edition: string;
            values: {
                WORKFLOW_ACTION_TYPE_INVALID: number;
                WORKFLOW_ACTION_TYPE_SMS: number;
                WORKFLOW_ACTION_TYPE_EMAIL: number;
                WORKFLOW_ACTION_TYPE_COMMENT: number;
                WORKFLOW_ACTION_TYPE_APPROVAL: number;
                WORKFLOW_ACTION_TYPE_ACKNOWLEDGE: number;
                WORKFLOW_ACTION_TYPE_SIGNATURE: number;
                WORKFLOW_ACTION_TYPE_TODO: number;
            };
        };
        WorkflowActionStatus: {
            edition: string;
            values: {
                WORKFLOW_ACTION_STATUS_INITIAL: number;
                WORKFLOW_ACTION_STATUS_RUNNING: number;
                WORKFLOW_ACTION_STATUS_COMPLETED: number;
                WORKFLOW_ACTION_STATUS_FAULTED: number;
            };
        };
        WorkflowAction: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                board_id: {
                    type: string;
                    id: number;
                };
                board_view_token: {
                    type: string;
                    id: number;
                };
                input: {
                    type: string;
                    id: number;
                };
                destination_board_id: {
                    type: string;
                    id: number;
                };
                output: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowCheckpoint: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                feed: {
                    type: string;
                    id: number;
                };
                action: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowStep: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                is_parallel_with_prev_step: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                enable_preparation: {
                    type: string;
                    id: number;
                };
                editor: {
                    type: string;
                    id: number;
                };
                hide_before_started: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                board_id: {
                    type: string;
                    id: number;
                };
                board_view_token: {
                    type: string;
                    id: number;
                };
                input: {
                    type: string;
                    id: number;
                };
                output: {
                    type: string;
                    id: number;
                };
                outgoings: {
                    rule: string;
                    type: string;
                    id: number;
                };
                outbound_data: {
                    type: string;
                    id: number;
                };
                inbound_data: {
                    type: string;
                    id: number;
                };
                parent_step: {
                    type: string;
                    id: number;
                };
                is_optional: {
                    type: string;
                    id: number;
                };
                preconditions: {
                    rule: string;
                    type: string;
                    id: number;
                };
                condition: {
                    type: string;
                    id: number;
                };
                checkpoints: {
                    rule: string;
                    type: string;
                    id: number;
                };
                is_holding: {
                    type: string;
                    id: number;
                };
                custom_data: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowObject: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowMilestone: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                order_number: {
                    type: string;
                    id: number;
                };
                processed_steps: {
                    type: string;
                    id: number;
                };
                total_steps: {
                    type: string;
                    id: number;
                };
                custom_data: {
                    type: string;
                    id: number;
                };
                preconditions: {
                    rule: string;
                    type: string;
                    id: number;
                };
                reverse_workflow_order: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WorkflowTriggerType: {
            edition: string;
            values: {
                WORKFLOW_TRIGGER_TYPE_NONE: number;
                WORKFLOW_TRIGGER_TYPE_WEBHOOK: number;
                WORKFLOW_TRIGGER_TYPE_APPS: number;
            };
        };
        WorkflowTriggerError: {
            edition: string;
            values: {
                WORKFLOW_TRIGGER_ERROR_NONE: number;
                WORKFLOW_TRIGGER_ERROR_INVALID_CREDENTIAL: number;
                WORKFLOW_TRIGGER_ERROR_SYSTEM: number;
            };
        };
        BoardWorkflow: {
            edition: string;
            fields: {
                creator: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                };
                template_board_id: {
                    type: string;
                    id: number;
                };
                original_template_board_id: {
                    type: string;
                    id: number;
                };
                update_from_original: {
                    type: string;
                    id: number;
                };
                template_name: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                welcome_msg: {
                    type: string;
                    id: number;
                };
                welcome_msg_comment_sequence: {
                    type: string;
                    id: number;
                };
                error_msg: {
                    type: string;
                    id: number;
                };
                error_code: {
                    type: string;
                    id: number;
                };
                status: {
                    type: string;
                    id: number;
                };
                definition: {
                    type: string;
                    id: number;
                };
                nodes: {
                    type: string;
                    id: number;
                };
                objects: {
                    rule: string;
                    type: string;
                    id: number;
                };
                processed_steps: {
                    type: string;
                    id: number;
                };
                total_steps: {
                    type: string;
                    id: number;
                };
                current_step: {
                    type: string;
                    id: number;
                };
                is_template: {
                    type: string;
                    id: number;
                };
                is_active: {
                    type: string;
                    id: number;
                };
                process_in_parallel: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                variables: {
                    rule: string;
                    type: string;
                    id: number;
                };
                steps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                milestones: {
                    rule: string;
                    type: string;
                    id: number;
                };
                outgoings: {
                    rule: string;
                    type: string;
                    id: number;
                };
                total_used_count: {
                    type: string;
                    id: number;
                };
                last_used_timestamp: {
                    type: string;
                    id: number;
                };
                last_modified_time: {
                    type: string;
                    id: number;
                };
                completed_time: {
                    type: string;
                    id: number;
                };
                original_signature: {
                    type: string;
                    id: number;
                };
                original_transaction: {
                    type: string;
                    id: number;
                };
                reference_id: {
                    type: string;
                    id: number;
                };
                trigger_type: {
                    type: string;
                    id: number;
                };
                trigger_activation_time: {
                    type: string;
                    id: number;
                };
                trigger_error: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                internal_board: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardType: {
            edition: string;
            values: {
                BOARD_TYPE_DEFAULT: number;
                BOARD_TYPE_WORKFLOW: number;
                BOARD_TYPE_WORKFLOW_TEMPLATE: number;
                BOARD_TYPE_CONTENT_LIBRARY_ACTION: number;
                BOARD_TYPE_CONTENT_LIBRARY_FILE: number;
                BOARD_TYPE_CONTENT_LIBRARY_MILESTONE: number;
                BOARD_TYPE_SCHEDULE: number;
                BOARD_TYPE_SELF_SERVICE_TEMPLATE: number;
                BOARD_TYPE_WORKFLOW_TEMPLATE_FOLDER: number;
                BOARD_TYPE_BROADCAST: number;
                BOARD_TYPE_AI: number;
            };
        };
        BoardProperty: {
            edition: string;
            fields: {
                group_sequence: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                value: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        WaitingRoomAudience: {
            edition: string;
            values: {
                WAITING_ROOM_AUDIENCE_GUEST: number;
                WAITING_ROOM_AUDIENCE_ALL: number;
            };
        };
        Board: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                islive: {
                    type: string;
                    id: number;
                };
                isconversation: {
                    type: string;
                    id: number;
                };
                isdefault: {
                    type: string;
                    id: number;
                };
                isnote: {
                    type: string;
                    id: number;
                };
                istemp: {
                    type: string;
                    id: number;
                };
                is_restricted: {
                    type: string;
                    id: number;
                };
                is_team: {
                    type: string;
                    id: number;
                };
                use_member_name_as_name: {
                    type: string;
                    id: number;
                };
                use_member_avatar_as_cover: {
                    type: string;
                    id: number;
                };
                is_relation: {
                    type: string;
                    id: number;
                };
                is_transaction: {
                    type: string;
                    id: number;
                };
                is_inactive: {
                    type: string;
                    id: number;
                };
                inactive_time: {
                    type: string;
                    id: number;
                };
                is_bot_relation: {
                    type: string;
                    id: number;
                };
                is_duplicate: {
                    type: string;
                    id: number;
                };
                is_file: {
                    type: string;
                    id: number;
                };
                is_todo: {
                    type: string;
                    id: number;
                };
                is_signature: {
                    type: string;
                    id: number;
                };
                is_flexible: {
                    type: string;
                    id: number;
                };
                is_external: {
                    type: string;
                    id: number;
                };
                iscall: {
                    type: string;
                    id: number;
                };
                is_inbox: {
                    type: string;
                    id: number;
                };
                is_personal_room: {
                    type: string;
                    id: number;
                };
                milliseconds_personal_room_waiting_timeout: {
                    type: string;
                    id: number;
                };
                is_app_subscription: {
                    type: string;
                    id: number;
                };
                is_shadow_flow: {
                    type: string;
                    id: number;
                };
                is_owner_delegate_enabled: {
                    type: string;
                    id: number;
                };
                social_type: {
                    type: string;
                    id: number;
                };
                is_client_editing_enabled: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                description: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                workspace_id: {
                    type: string;
                    id: number;
                };
                invite_code: {
                    type: string;
                    id: number;
                };
                thumbnail_need_migrate: {
                    type: string;
                    id: number;
                };
                thumbnail: {
                    type: string;
                    id: number;
                };
                thumbnail_view_token: {
                    type: string;
                    id: number;
                };
                banner: {
                    type: string;
                    id: number;
                };
                banner_mobile: {
                    type: string;
                    id: number;
                };
                board_pdf: {
                    type: string;
                    id: number;
                };
                board_ppt: {
                    type: string;
                    id: number;
                };
                board_recording: {
                    type: string;
                    id: number;
                };
                thumbnail_source_page: {
                    type: string;
                    id: number;
                };
                thumbnail_source_resource: {
                    type: string;
                    id: number;
                };
                email_address: {
                    type: string;
                    id: number;
                };
                phone_number: {
                    type: string;
                    id: number;
                };
                is_acd: {
                    type: string;
                    id: number;
                };
                is_service_request: {
                    type: string;
                    id: number;
                };
                routing_status: {
                    type: string;
                    id: number;
                };
                routing_channel: {
                    type: string;
                    id: number;
                };
                is_channel_subscription: {
                    type: string;
                    id: number;
                };
                is_content_library: {
                    type: string;
                    id: number;
                };
                is_client_resources: {
                    type: string;
                    id: number;
                };
                enable_waiting_room: {
                    type: string;
                    id: number;
                };
                waiting_room_audience: {
                    type: string;
                    id: number;
                };
                teams: {
                    rule: string;
                    type: string;
                    id: number;
                };
                pages: {
                    rule: string;
                    type: string;
                    id: number;
                };
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
                tags: {
                    rule: string;
                    type: string;
                    id: number;
                };
                page_groups: {
                    rule: string;
                    type: string;
                    id: number;
                };
                view_tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                folders: {
                    rule: string;
                    type: string;
                    id: number;
                };
                reference_links: {
                    rule: string;
                    type: string;
                    id: number;
                };
                users: {
                    rule: string;
                    type: string;
                    id: number;
                };
                owner: {
                    type: string;
                    id: number;
                };
                user_rsvps: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sessions: {
                    rule: string;
                    type: string;
                    id: number;
                };
                calls: {
                    rule: string;
                    type: string;
                    id: number;
                };
                comments: {
                    rule: string;
                    type: string;
                    id: number;
                };
                todos: {
                    rule: string;
                    type: string;
                    id: number;
                };
                signatures: {
                    rule: string;
                    type: string;
                    id: number;
                };
                transactions: {
                    rule: string;
                    type: string;
                    id: number;
                };
                workflows: {
                    rule: string;
                    type: string;
                    id: number;
                };
                feeds: {
                    rule: string;
                    type: string;
                    id: number;
                };
                waiting_users: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_activities: {
                    rule: string;
                    type: string;
                    id: number;
                };
                user_activities_last: {
                    type: string;
                    id: number;
                };
                pin_editor_type: {
                    type: string;
                    id: number;
                };
                pins: {
                    rule: string;
                    type: string;
                    id: number;
                };
                reminders: {
                    rule: string;
                    type: string;
                    id: number;
                };
                requesting_users: {
                    rule: string;
                    type: string;
                    id: number;
                };
                total_pages: {
                    type: string;
                    id: number;
                };
                total_members: {
                    type: string;
                    id: number;
                };
                total_comments: {
                    type: string;
                    id: number;
                };
                total_todos: {
                    type: string;
                    id: number;
                };
                total_open_todos: {
                    type: string;
                    id: number;
                };
                has_folder: {
                    type: string;
                    id: number;
                };
                total_signatures: {
                    type: string;
                    id: number;
                };
                total_emails: {
                    type: string;
                    id: number;
                };
                total_hits: {
                    type: string;
                    id: number;
                };
                total_creators: {
                    type: string;
                    id: number;
                };
                total_transactions: {
                    type: string;
                    id: number;
                };
                total_pins: {
                    type: string;
                    id: number;
                };
                total_open_signatures: {
                    type: string;
                    id: number;
                };
                total_open_transactions: {
                    type: string;
                    id: number;
                };
                total_size: {
                    type: string;
                    id: number;
                };
                access_control_board_id: {
                    type: string;
                    id: number;
                };
                board_member_notification_settings: {
                    type: string;
                    id: number;
                };
                action_notification_settings: {
                    type: string;
                    id: number;
                };
                board_notification_settings: {
                    type: string;
                    id: number;
                };
                archive_after: {
                    type: string;
                    id: number;
                };
                properties: {
                    rule: string;
                    type: string;
                    id: number;
                };
                previous_due_date: {
                    type: string;
                    id: number;
                };
                due_date: {
                    type: string;
                    id: number;
                };
                due_in_timeframe: {
                    type: string;
                    id: number;
                };
                exclude_weekends: {
                    type: string;
                    id: number;
                };
                original_board_id: {
                    type: string;
                    id: number;
                };
                broadcasts: {
                    rule: string;
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                index_local_field_after_revision: {
                    type: string;
                    id: number;
                };
                index_revision: {
                    type: string;
                    id: number;
                };
                index_version: {
                    type: string;
                    id: number;
                };
                revision_in_previous_update_member_job: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        CombinedPushNotificationPayload: {
            edition: string;
            fields: {
                payload: {
                    type: string;
                    id: number;
                };
                data: {
                    type: string;
                    id: number;
                };
            };
        };
        ApplePushNotification: {
            edition: string;
            fields: {
                payload: {
                    type: string;
                    id: number;
                };
                apple_device_token: {
                    type: string;
                    id: number;
                };
                apple_device_tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                message_sequence: {
                    type: string;
                    id: number;
                };
                user_id: {
                    type: string;
                    id: number;
                };
                client_id: {
                    type: string;
                    id: number;
                };
                is_voip: {
                    type: string;
                    id: number;
                };
                registration_ids: {
                    rule: string;
                    type: string;
                    id: number;
                };
                data: {
                    type: string;
                    id: number;
                };
            };
        };
        ApplePushNotificationPayload: {
            edition: string;
            fields: {
                aps: {
                    type: string;
                    id: number;
                };
                request: {
                    type: string;
                    id: number;
                };
                id: {
                    type: string;
                    id: number;
                };
                moxtra: {
                    type: string;
                    id: number;
                };
                sender_org_id: {
                    type: string;
                    id: number;
                };
                sender_unique_id: {
                    type: string;
                    id: number;
                };
                sender_name: {
                    type: string;
                    id: number;
                };
                receiver_org_id: {
                    type: string;
                    id: number;
                };
                receiver_unique_id: {
                    type: string;
                    id: number;
                };
                receiver_language: {
                    type: string;
                    id: number;
                };
                receiver_email: {
                    type: string;
                    id: number;
                };
                receiver_phone_number: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
                board_id: {
                    type: string;
                    id: number;
                };
                is_privacy: {
                    type: string;
                    id: number;
                };
                original_loc_key: {
                    type: string;
                    id: number;
                };
                original_action_loc_key: {
                    type: string;
                    id: number;
                };
                extended_loc_key: {
                    type: string;
                    id: number;
                };
                workspace_type: {
                    type: string;
                    id: number;
                };
            };
        };
        ApplePushNotificationAps: {
            edition: string;
            fields: {
                alert: {
                    type: string;
                    id: number;
                };
                badge: {
                    type: string;
                    id: number;
                };
                sound: {
                    type: string;
                    id: number;
                };
                content_available: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
            };
        };
        ApplePushNotificationAlert: {
            edition: string;
            fields: {
                title: {
                    type: string;
                    id: number;
                };
                body: {
                    type: string;
                    id: number;
                };
                action_loc_key: {
                    type: string;
                    id: number;
                };
                loc_key: {
                    type: string;
                    id: number;
                };
                loc_args: {
                    rule: string;
                    type: string;
                    id: number;
                };
                launch_image: {
                    type: string;
                    id: number;
                };
            };
        };
        ApplePushNotificationResponse: {
            edition: string;
            fields: {
                reason: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
            };
        };
        GCMPushNotification: {
            edition: string;
            fields: {
                registration_ids: {
                    rule: string;
                    type: string;
                    id: number;
                };
                data: {
                    type: string;
                    id: number;
                };
                client_id: {
                    type: string;
                    id: number;
                };
                priority: {
                    type: string;
                    id: number;
                };
                restricted_package_name: {
                    type: string;
                    id: number;
                };
                notification: {
                    type: string;
                    id: number;
                };
            };
        };
        GCMPushNotificationMessage: {
            edition: string;
            fields: {
                title: {
                    type: string;
                    id: number;
                };
                body: {
                    type: string;
                    id: number;
                };
                click_action: {
                    type: string;
                    id: number;
                };
                android_channel_id: {
                    type: string;
                    id: number;
                };
                tag: {
                    type: string;
                    id: number;
                };
            };
        };
        GCMPushNotificationData: {
            edition: string;
            fields: {
                body: {
                    type: string;
                    id: number;
                };
                action_loc_key: {
                    type: string;
                    id: number;
                };
                loc_key: {
                    type: string;
                    id: number;
                };
                arg1: {
                    type: string;
                    id: number;
                };
                arg2: {
                    type: string;
                    id: number;
                };
                arg3: {
                    type: string;
                    id: number;
                };
                arg4: {
                    type: string;
                    id: number;
                };
                badge: {
                    type: string;
                    id: number;
                };
                sound: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                board_id: {
                    type: string;
                    id: number;
                };
                page_sequence: {
                    type: string;
                    id: number;
                };
                feed_sequence: {
                    type: string;
                    id: number;
                };
                user_id: {
                    type: string;
                    id: number;
                };
                id: {
                    type: string;
                    id: number;
                };
                moxtra: {
                    type: string;
                    id: number;
                };
                request: {
                    type: string;
                    id: number;
                };
                title: {
                    type: string;
                    id: number;
                };
                board_name: {
                    type: string;
                    id: number;
                };
                board_feed_unread_count: {
                    type: string;
                    id: number;
                };
                sender_org_id: {
                    type: string;
                    id: number;
                };
                sender_unique_id: {
                    type: string;
                    id: number;
                };
                sender_name: {
                    type: string;
                    id: number;
                };
                receiver_org_id: {
                    type: string;
                    id: number;
                };
                receiver_unique_id: {
                    type: string;
                    id: number;
                };
                receiver_language: {
                    type: string;
                    id: number;
                };
                receiver_email: {
                    type: string;
                    id: number;
                };
                receiver_phone_number: {
                    type: string;
                    id: number;
                };
                category: {
                    type: string;
                    id: number;
                };
                is_privacy: {
                    type: string;
                    id: number;
                };
                original_loc_key: {
                    type: string;
                    id: number;
                };
                original_action_loc_key: {
                    type: string;
                    id: number;
                };
                extended_loc_key: {
                    type: string;
                    id: number;
                };
                workspace_type: {
                    type: string;
                    id: number;
                };
            };
        };
        GCMPushNotificationResponse: {
            edition: string;
            fields: {
                multicast_id: {
                    type: string;
                    id: number;
                };
                success: {
                    type: string;
                    id: number;
                };
                failure: {
                    type: string;
                    id: number;
                };
                canonical_ids: {
                    type: string;
                    id: number;
                };
                results: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        GCMResult: {
            edition: string;
            fields: {
                message_id: {
                    type: string;
                    id: number;
                };
                registration_id: {
                    type: string;
                    id: number;
                };
                error: {
                    type: string;
                    id: number;
                };
            };
        };
        PushNotificationProxyResponse: {
            edition: string;
            fields: {
                message: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                user_id: {
                    type: string;
                    id: number;
                };
                bad_apple_device_tokens: {
                    rule: string;
                    type: string;
                    id: number;
                };
                bad_registration_ids: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        ObjectFeedType: {
            edition: string;
            values: {
                FEED_INVALID: number;
                FEED_BOARD_CREATE: number;
                FEED_BOARD_NAME_CHANGE: number;
                FEED_BOARD_COMMENT: number;
                FEED_BOARD_VOICE_COMMENT: number;
                FEED_BOARD_COMMENT_DELETE: number;
                FEED_BOARD_DUE_DATE_UPDATE: number;
                FEED_BOARD_DUE_DATE_ARRIVE: number;
                FEED_PAGES_CREATE: number;
                FEED_PAGES_CREATE_WITH_ANNOTATION: number;
                FEED_PAGES_ANNOTATION: number;
                FEED_PAGES_UPDATE: number;
                FEED_PAGES_DELETE: number;
                FEED_PAGES_COMMENT: number;
                FEED_PAGES_POSITION_COMMENT: number;
                FEED_PAGES_COMMENT_DELETE: number;
                FEED_PAGES_RENAME: number;
                FEED_PAGES_RECYCLE: number;
                FEED_PAGES_MOVE: number;
                FEED_EMAIL_RECEIVE: number;
                FEED_RELATIONSHIP_JOIN: number;
                FEED_RELATIONSHIP_LEAVE: number;
                FEED_RELATIONSHIP_INVITE: number;
                FEED_RELATIONSHIP_DECLINE: number;
                FEED_RELATIONSHIP_CANCEL: number;
                FEED_RELATIONSHIP_REMOVE: number;
                FEED_RELATIONSHIP_CHANGE_ROLE: number;
                FEED_TODO_CREATE: number;
                FEED_TODO_CREATE_WITH_RESOURCE: number;
                FEED_TODO_UPDATE: number;
                FEED_TODO_DELETE: number;
                FEED_TODO_ASSIGN: number;
                FEED_TODO_COMMENT: number;
                FEED_TODO_ATTACHMENT: number;
                FEED_TODO_DUE_DATE: number;
                FEED_TODO_COMPLETE: number;
                FEED_TODO_REOPEN: number;
                FEED_TODO_DUE_DATE_ARRIVE: number;
                FEED_TODO_COMMENT_DELETE: number;
                FEED_TODO_MARK_AS_COMPLETED: number;
                FEED_FOLDER_CREATE: number;
                FEED_FOLDER_RENAME: number;
                FEED_FOLDER_RECYCLE: number;
                FEED_FOLDER_DELETE: number;
                FEED_SESSION_SCHEDULE: number;
                FEED_SESSION_RESCHEDULE: number;
                FEED_SESSION_START: number;
                FEED_SESSION_END: number;
                FEED_SESSION_RECORDING_READY: number;
                FEED_SESSION_CANCEL: number;
                FEED_SESSION_RENAME: number;
                FEED_NOTE_CREATE: number;
                FEED_PIN: number;
                FEED_CALL_LOG: number;
                FEED_AUDIO_CALL_LOG: number;
                FEED_RELATIONSHIP_INVITE_PENDING: number;
                FEED_RELATIONSHIP_REMOVE_PENDING: number;
                FEED_RELATIONSHIP_JOIN_PENDING: number;
                FEED_REQUESTING_USER_CREATE: number;
                FEED_REQUESTING_USER_UPDATE: number;
                FEED_SIGNATURE_STATUS_UPDATE: number;
                FEED_SIGNATURE_DELETE: number;
                FEED_SIGNATURE_RENAME: number;
                FEED_SIGNATURE_CONVERTED: number;
                FEED_SIGNATURE_DUE_DATE_UPDATE: number;
                FEED_SIGNATURE_DUE_DATE_ARRIVE: number;
                FEED_SIGNATURE_FILE_REPLY: number;
                FEED_SIGNATURE_UPDATE: number;
                FEED_SIGNATURE_REOPEN: number;
                FEED_SIGNATURE_UPDATE_REOPEN: number;
                FEED_VIEWTOKEN_CREATE: number;
                FEED_VIEWTOKEN_DELETE: number;
                FEED_VIEWTOKEN_UPDATE: number;
                FEED_SIGNATURE_UPDATE_EDITING: number;
                FEED_SIGNATURE_UPDATE_READY: number;
                FEED_SIGNATURE_MARK_AS_COMPLETED: number;
                FEED_TRANSACTION_CREATE: number;
                FEED_TRANSACTION_DELETE: number;
                FEED_TRANSACTION_UPDATE: number;
                FEED_TRANSACTION_UPDATE_REOPEN: number;
                FEED_TRANSACTION_STEP_SUBMIT: number;
                FEED_TRANSACTION_ATTACHMENT: number;
                FEED_TRANSACTION_EXPIRATION_DATE_ARRIVE: number;
                FEED_TRANSACTION_STEP_SUBMIT_BATCH: number;
                FEED_TRANSACTION_STEP_REOPEN: number;
                FEED_TRANSACTION_EXPIRATION_UPDATE: number;
                FEED_TRANSACTION_FILE_REPLY: number;
                FEED_TRANSACTION_REOPEN: number;
                FEED_TRANSACTION_FORM_CONVERTED: number;
                FEED_TRANSACTION_UPDATE_CUSTOM_RESULT: number;
                FEED_TRANSACTION_UPDATE_EDITING: number;
                FEED_TRANSACTION_UPDATE_READY: number;
                FEED_TRANSACTION_MARK_AS_COMPLETED: number;
                FEED_WORKFLOW_STEP_PREPARING: number;
                FEED_WORKFLOW_STEP_READY: number;
                FEED_WORKFLOW_STEP_SKIPPED: number;
                FEED_WORKFLOW_STEP_REOPEN: number;
                FEED_WORKFLOW_STARTED: number;
                FEED_WORKFLOW_RESTARTED: number;
                FEED_WORKFLOW_COMPLETED: number;
                FEED_WORKFLOW_CANCELED: number;
                FEED_WORKFLOW_CONTINUED: number;
                FEED_WORKFLOW_CREATE: number;
                FEED_WORKFLOW_UPDATE: number;
                FEED_WORKFLOW_DELETE: number;
                FEED_REASSIGN: number;
                FEED_SERVICE_REQUEST_CREATE: number;
                FEED_SERVICE_REQUEST_UPDATE: number;
                FEED_SERVICE_REQUEST_COMPLETE: number;
                FEED_SERVICE_REQUEST_REOPEN: number;
                FEED_ACD_REQUEST_END: number;
                FEED_ACD_REQUEST_TIMEOUT: number;
            };
        };
        ObjectFeedStatus: {
            edition: string;
            values: {
                FEED_STATUS_INVALID: number;
                FEED_STATUS_PENDING: number;
                FEED_STATUS_APPROVED: number;
                FEED_STATUS_DENIED: number;
            };
        };
        ObjectFeedViaSource: {
            edition: string;
            values: {
                FEED_VIA_UNKNOWN: number;
                FEED_VIA_IOS: number;
                FEED_VIA_ANDROID: number;
                FEED_VIA_WEB: number;
                FEED_VIA_DESKTOP: number;
                FEED_VIA_WORKFLOW: number;
            };
        };
        FeedReaction: {
            edition: string;
            fields: {
                creator: {
                    type: string;
                    id: number;
                };
                text: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
            };
        };
        ObjectFeed: {
            edition: string;
            fields: {
                actor: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                view_token: {
                    type: string;
                    id: number;
                };
                roster: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                board: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                is_pinned: {
                    type: string;
                    id: number;
                };
                delegate: {
                    type: string;
                    id: number;
                    options: {
                        lazy: boolean;
                    };
                };
                status: {
                    type: string;
                    id: number;
                };
                via: {
                    type: string;
                    id: number;
                };
                reactions: {
                    rule: string;
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        ObjectActivity: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                server: {
                    type: string;
                    id: number;
                };
                actor: {
                    type: string;
                    id: number;
                };
                request_object: {
                    type: string;
                    id: number;
                };
                object: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        ObjectRecording: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                activities: {
                    rule: string;
                    type: string;
                    id: number;
                };
                recording_count: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        AudioRecording: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                hash: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
            };
        };
        VideoRecording: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                hash: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
            };
        };
        DsRecording: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                name: {
                    type: string;
                    id: number;
                };
                server_addr: {
                    type: string;
                    id: number;
                };
            };
        };
        PublicViewTokenType: {
            edition: string;
            values: {
                EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL: number;
                EMAIL_TOKEN_PICTURE_PAGE_THUMBNAIL: number;
                EMAIL_TOKEN_PICTURE_USER_PICTURE: number;
                EMAIL_TOKEN_VIDEO_COMMENT_HASH: number;
                EMAIL_TOKEN_VIDEO_COMMENT: number;
                EMAIL_TOKEN_VERIFY_USER_EMAIL: number;
                EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION: number;
                EMAIL_TOKEN_PICTURE_BOARD_THUMBNAIL_INVIATION_NON_REGISTERED: number;
                EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION: number;
                EMAIL_TOKEN_PICTURE_USER_PICTURE_INVIATION_NON_REGISTERED: number;
                PUBLIC_VIEW_TOKEN_BOARD: number;
                TRANSACTION_VIEW_TOKEN: number;
                BOARD_RESOURCE_VIEW_TOKEN: number;
                BOARD_PUBLIC_ACCESS_TOKEN: number;
                GROUP_INVITATION_TOKEN: number;
                PARTNER_INVITATION_TOKEN: number;
                BOARD_INVITATION_TOKEN: number;
                CONTACT_INVITATION_TOKEN: number;
                SESSION_ROSTER_TOKEN: number;
                GROUP_USER_VIEW_TOKEN: number;
                GROUP_USER_INVITATION_TOKEN: number;
            };
        };
        PublicViewToken: {
            edition: string;
            fields: {
                version: {
                    type: string;
                    id: number;
                };
                token: {
                    type: string;
                    id: number;
                };
                user_id: {
                    type: string;
                    id: number;
                };
                board_id: {
                    type: string;
                    id: number;
                };
                board_token: {
                    type: string;
                    id: number;
                };
                session_key: {
                    type: string;
                    id: number;
                };
                roster_index: {
                    type: string;
                    id: number;
                };
                roster_channel: {
                    type: string;
                    id: number;
                };
                type: {
                    type: string;
                    id: number;
                };
                resource_hash: {
                    type: string;
                    id: number;
                };
                resource_seq: {
                    type: string;
                    id: number;
                };
                resource_origin: {
                    type: string;
                    id: number;
                };
                actor_id: {
                    type: string;
                    id: number;
                };
                feed_seq: {
                    type: string;
                    id: number;
                };
                contact_seq: {
                    type: string;
                    id: number;
                };
                transaction_seq: {
                    type: string;
                    id: number;
                };
                boarduser_seq: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                user_email: {
                    type: string;
                    id: number;
                };
                user_phone_number: {
                    type: string;
                    id: number;
                };
                group_id: {
                    type: string;
                    id: number;
                };
                groupuser_seq: {
                    type: string;
                    id: number;
                };
                invitation_token_seq: {
                    type: string;
                    id: number;
                };
                invitation_token_created_timestamp: {
                    type: string;
                    id: number;
                };
                partner_id: {
                    type: string;
                    id: number;
                };
                token_created_timestamp: {
                    type: string;
                    id: number;
                };
                token_expire_timestamp: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupUsageItemType: {
            edition: string;
            values: {
                GROUP_LICENSE_USAGE_TYPE: number;
                GROUP_LICENSE_USAGE_TYPE_LOCAL: number;
            };
        };
        GroupUsageItem: {
            edition: string;
            fields: {
                group: {
                    type: string;
                    id: number;
                };
                item_type: {
                    type: string;
                    id: number;
                };
                timestamp: {
                    type: string;
                    id: number;
                };
                quantity_used: {
                    type: string;
                    id: number;
                };
                quantity_active: {
                    type: string;
                    id: number;
                };
                quantity_unassigned: {
                    type: string;
                    id: number;
                };
                quantity_committed: {
                    type: string;
                    id: number;
                };
                quantity: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        GroupUsageCount: {
            edition: string;
            fields: {
                partners: {
                    type: string;
                    id: number;
                };
                groups: {
                    type: string;
                    id: number;
                };
                users: {
                    type: string;
                    id: number;
                };
            };
        };
        SessionUsageItem: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                };
                session: {
                    type: string;
                    id: number;
                };
                total_minutes: {
                    type: string;
                    id: number;
                };
                total_telephony_minutes: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        BoardUsageItem: {
            edition: string;
            fields: {
                board: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        UsageStatistics: {
            edition: string;
            fields: {
                id: {
                    type: string;
                    id: number;
                };
                group_usage_items: {
                    rule: string;
                    type: string;
                    id: number;
                };
                session_items: {
                    rule: string;
                    type: string;
                    id: number;
                };
                total_minutes: {
                    type: string;
                    id: number;
                };
                total_telephony_minutes: {
                    type: string;
                    id: number;
                };
                board_items: {
                    rule: string;
                    type: string;
                    id: number;
                };
                group_counts: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        ActivityLog: {
            edition: string;
            fields: {
                token: {
                    type: string;
                    id: number;
                };
                request: {
                    type: string;
                    id: number;
                };
                response: {
                    type: string;
                    id: number;
                };
                user: {
                    type: string;
                    id: number;
                };
                stats: {
                    type: string;
                    id: number;
                };
                logs: {
                    rule: string;
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
            };
        };
        ActivityStatistics: {
            edition: string;
            fields: {
                start_time: {
                    type: string;
                    id: number;
                };
                end_time: {
                    type: string;
                    id: number;
                };
                app_stats: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryLeftSidePanel: {
            edition: string;
            fields: {
                click_clients: {
                    type: string;
                    id: number;
                };
                click_internal: {
                    type: string;
                    id: number;
                };
                click_files: {
                    type: string;
                    id: number;
                };
                click_files_plus: {
                    type: string;
                    id: number;
                };
                click_meetings: {
                    type: string;
                    id: number;
                };
                click_meetings_plus: {
                    type: string;
                    id: number;
                };
                click_esign: {
                    type: string;
                    id: number;
                };
                click_esign_plus: {
                    type: string;
                    id: number;
                };
                click_todos: {
                    type: string;
                    id: number;
                };
                click_todos_plus: {
                    type: string;
                    id: number;
                };
                click_contacts: {
                    type: string;
                    id: number;
                };
                click_broadcast: {
                    type: string;
                    id: number;
                };
                click_updates: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryMainNewPlusPanel: {
            edition: string;
            fields: {
                click_file: {
                    type: string;
                    id: number;
                };
                click_esign: {
                    type: string;
                    id: number;
                };
                click_todos: {
                    type: string;
                    id: number;
                };
                click_start_meeting: {
                    type: string;
                    id: number;
                };
                click_schedule_meeting: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryTopNavBar: {
            edition: string;
            fields: {
                click_manage: {
                    type: string;
                    id: number;
                };
                click_global_search: {
                    type: string;
                    id: number;
                };
                click_action_items: {
                    type: string;
                    id: number;
                };
                click_mentions: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryMentionList: {
            edition: string;
            fields: {
                click_dismiss_all: {
                    type: string;
                    id: number;
                };
                click_dismiss_item: {
                    type: string;
                    id: number;
                };
                click_mention_item: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryActionItems: {
            edition: string;
            fields: {
                click_action_item: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryTimeline: {
            edition: string;
            fields: {
                click_action_items: {
                    type: string;
                    id: number;
                };
                click_mentions: {
                    type: string;
                    id: number;
                };
                click_global_search: {
                    type: string;
                    id: number;
                };
                click_filter_conversation: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryBinderView: {
            edition: string;
            fields: {
                click_meeting: {
                    type: string;
                    id: number;
                };
                click_search: {
                    type: string;
                    id: number;
                };
                click_files_tab: {
                    type: string;
                    id: number;
                };
                click_todo_tab: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryOverview: {
            edition: string;
            fields: {
                click_overview_tab: {
                    type: string;
                    id: number;
                };
                click_files_view_all: {
                    type: string;
                    id: number;
                };
                click_file_item: {
                    type: string;
                    id: number;
                };
                click_esign_view_all: {
                    type: string;
                    id: number;
                };
                click_sign_now: {
                    type: string;
                    id: number;
                };
                click_todos_view_all: {
                    type: string;
                    id: number;
                };
                click_todo_item: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatCategoryNewFlowWorkspace: {
            edition: string;
            fields: {
                total_launched_by_webhook: {
                    type: string;
                    id: number;
                };
                total_launched_by_webhook_with_newly_invited_client: {
                    type: string;
                    id: number;
                };
                total_launched_from_template: {
                    type: string;
                    id: number;
                };
                total_launched_from_template_with_newly_invited_client: {
                    type: string;
                    id: number;
                };
                total_launched_from_plus_new: {
                    type: string;
                    id: number;
                };
                total_launched_from_plus_new_with_newly_invited_client: {
                    type: string;
                    id: number;
                };
                total_launched_from_scheduled_flow: {
                    type: string;
                    id: number;
                };
                total_launched_from_scheduled_flow_with_newly_invited_client: {
                    type: string;
                    id: number;
                };
                total_launched_by_zapier: {
                    type: string;
                    id: number;
                };
                total_launched_by_zapier_with_newly_invited_client: {
                    type: string;
                    id: number;
                };
                total_launched_by_rest_api: {
                    type: string;
                    id: number;
                };
                total_launched_by_rest_api_with_newly_invited_client: {
                    type: string;
                    id: number;
                };
                total_launched_from_sr: {
                    type: string;
                    id: number;
                };
                total_launched_from_main_flow: {
                    type: string;
                    id: number;
                };
            };
        };
        AppStatistics: {
            edition: string;
            fields: {
                web_left_side_panel: {
                    type: string;
                    id: number;
                };
                web_main_new_plus_panel: {
                    type: string;
                    id: number;
                };
                web_top_nav_bar: {
                    type: string;
                    id: number;
                };
                ios_mention_list: {
                    type: string;
                    id: number;
                };
                android_mention_list: {
                    type: string;
                    id: number;
                };
                web_mention_list: {
                    type: string;
                    id: number;
                };
                ios_action_items: {
                    type: string;
                    id: number;
                };
                android_action_items: {
                    type: string;
                    id: number;
                };
                web_action_items: {
                    type: string;
                    id: number;
                };
                ios_timeline: {
                    type: string;
                    id: number;
                };
                android_timeline: {
                    type: string;
                    id: number;
                };
                ios_binder_view: {
                    type: string;
                    id: number;
                };
                android_binder_view: {
                    type: string;
                    id: number;
                };
                ios_overview: {
                    type: string;
                    id: number;
                };
                android_overview: {
                    type: string;
                    id: number;
                };
                ios_new_flow_workspace: {
                    type: string;
                    id: number;
                };
                android_new_flow_workspace: {
                    type: string;
                    id: number;
                };
                web_new_flow_workspace: {
                    type: string;
                    id: number;
                };
            };
        };
        ResourceItem: {
            edition: string;
            fields: {
                hash: {
                    type: string;
                    id: number;
                };
                accessed_time: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                content_length: {
                    type: string;
                    id: number;
                };
            };
        };
        Resources: {
            edition: string;
            fields: {
                resources: {
                    rule: string;
                    type: string;
                    id: number;
                };
            };
        };
        TwilioRequestParam: {
            edition: string;
            fields: {
                call_sid: {
                    type: string;
                    id: number;
                };
                account_sid: {
                    type: string;
                    id: number;
                };
                from: {
                    type: string;
                    id: number;
                };
                to: {
                    type: string;
                    id: number;
                };
                p_asserted_identity: {
                    type: string;
                    id: number;
                };
                call_status: {
                    type: string;
                    id: number;
                };
                api_version: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemEmailConfig: {
            edition: string;
            fields: {
                username: {
                    type: string;
                    id: number;
                };
                password: {
                    type: string;
                    id: number;
                };
                server_address: {
                    type: string;
                    id: number;
                };
                server_port: {
                    type: string;
                    id: number;
                };
                from_address: {
                    type: string;
                    id: number;
                };
                default_incoming_domain: {
                    type: string;
                    id: number;
                };
                incoming_domains: {
                    rule: string;
                    type: string;
                    id: number;
                };
                alert_emails: {
                    rule: string;
                    type: string;
                    id: number;
                };
                feedback_email: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemAdminUser: {
            edition: string;
            fields: {
                user: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemUdpMapping: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                domain: {
                    type: string;
                    id: number;
                };
                port: {
                    type: string;
                    id: number;
                };
                tcp_port: {
                    type: string;
                    id: number;
                };
                url: {
                    type: string;
                    id: number;
                };
                ds_url: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemAppMapping: {
            edition: string;
            fields: {
                name: {
                    type: string;
                    id: number;
                };
                client_id: {
                    type: string;
                    id: number;
                };
                sequence: {
                    type: string;
                    id: number;
                };
                client_uuid: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemDocumentConverter: {
            edition: string;
            fields: {
                wopi_url: {
                    type: string;
                    id: number;
                };
                word_viewer_url: {
                    type: string;
                    id: number;
                };
                ppt_viewer_url: {
                    type: string;
                    id: number;
                };
                ppt_param_pid: {
                    type: string;
                    id: number;
                };
                mac_viewer_url: {
                    type: string;
                    id: number;
                };
                resource_domain: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemFeatures: {
            edition: string;
            fields: {
                disable_register_login: {
                    type: string;
                    id: number;
                };
                disable_meet_recording_feed: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemPasswordRule: {
            edition: string;
            fields: {
                character: {
                    type: string;
                    id: number;
                };
                lowercase: {
                    type: string;
                    id: number;
                };
                uppercase: {
                    type: string;
                    id: number;
                };
                digit: {
                    type: string;
                    id: number;
                };
                special: {
                    type: string;
                    id: number;
                };
                special_characters: {
                    type: string;
                    id: number;
                };
            };
        };
        SystemConfig: {
            edition: string;
            fields: {
                domain: {
                    type: string;
                    id: number;
                };
                timezone: {
                    type: string;
                    id: number;
                    options: {
                        default: string;
                    };
                };
                email_config: {
                    type: string;
                    id: number;
                };
                default_plan_cap: {
                    type: string;
                    id: number;
                };
                plans: {
                    rule: string;
                    type: string;
                    id: number;
                };
                app_mappings: {
                    rule: string;
                    type: string;
                    id: number;
                };
                udp_mappings: {
                    rule: string;
                    type: string;
                    id: number;
                };
                document_converter: {
                    type: string;
                    id: number;
                };
                users: {
                    rule: string;
                    type: string;
                    id: number;
                };
                features: {
                    type: string;
                    id: number;
                };
                password_rule: {
                    type: string;
                    id: number;
                };
                validation_code: {
                    type: string;
                    id: number;
                };
                is_validated: {
                    type: string;
                    id: number;
                    options: {
                        default: boolean;
                    };
                };
                validation_expires_after: {
                    type: string;
                    id: number;
                };
                revision: {
                    type: string;
                    id: number;
                };
                is_deleted: {
                    type: string;
                    id: number;
                };
                local_revision: {
                    type: string;
                    id: number;
                };
                assignments: {
                    type: string;
                    id: number;
                };
                created_time: {
                    type: string;
                    id: number;
                };
                updated_time: {
                    type: string;
                    id: number;
                };
            };
        };
    };
};
//# sourceMappingURL=ProtoBufDefineJSON.d.ts.map