var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var _a;
import { uuid, SessionStorage, LocalStorage, IndexedDBStorage } from '@moxo/shared';
export var CacheType;
(function (CacheType) {
    CacheType["sessionStorage"] = "sessionStorage";
    CacheType["localStorage"] = "localStorage";
    CacheType["indexDB"] = "indexDB";
})(CacheType || (CacheType = {}));
const SysCacheSymbol = Symbol('cacheData');
export class SysCache {
    constructor(option = {}) {
        Object.defineProperty(this, "subscriber", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, _a, {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "option", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "db", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        }); // 使用明确赋值断言，因为在 init() 方法中初始化
        Object.defineProperty(this, "inited", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.subscriber = new Map();
        this.option = Object.assign({ saveOnUpdate: true }, option);
        this[SysCacheSymbol] = {}; // 初始化缓存数据
        this.inited = false;
    }
    init() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.inited) {
                return;
            }
            switch (this.option.cacheType) {
                case CacheType.indexDB:
                    const { dbName, cacheKey, version } = this.option;
                    if (!dbName || !cacheKey || !version) {
                        throw Error('dbName,cacheKey,version is required for IndexDBStorage!');
                    }
                    this.db = new IndexedDBStorage(dbName, cacheKey, version);
                    break;
                case CacheType.localStorage:
                    this.db = new LocalStorage();
                    break;
                case CacheType.sessionStorage:
                    this.db = new SessionStorage();
                    break;
            }
            if (this.db) {
                this[SysCacheSymbol] = (yield this.db.getItem(this.option.cacheKey)) || {};
                window.addEventListener('beforeunload', () => {
                    this.db.setItem(this.option.cacheKey, this[SysCacheSymbol]);
                });
            }
            else {
                this[SysCacheSymbol] = {};
            }
        });
    }
    clear() {
        this[SysCacheSymbol] = {};
        if (this.db) {
            this.db.removeItem(this.option.cacheKey);
        }
    }
    subscribe(fn) {
        this.init();
        const id = uuid();
        this.subscriber.set(id, fn);
        return id;
    }
    unsubscribe(id) {
        this.subscriber.delete(id);
    }
    update(fn) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.init();
            const data = this[SysCacheSymbol];
            this[SysCacheSymbol] = fn(data);
            this.subscriber.forEach((fn) => fn());
            if (this.option.saveOnUpdate) {
                this.db.setItem(this.option.cacheKey, this[SysCacheSymbol]);
            }
        });
    }
}
_a = SysCacheSymbol;
const UserCache = new SysCache({
    cacheKey: 'm-user',
    cacheType: CacheType.sessionStorage,
});
const GroupCache = new SysCache({
    cacheKey: 'm-user-org',
    cacheType: CacheType.indexDB,
    dbName: 'user-org',
    version: 1,
});
export { UserCache, GroupCache };
