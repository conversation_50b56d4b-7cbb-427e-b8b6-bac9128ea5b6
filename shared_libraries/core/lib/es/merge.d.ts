export declare function mergeCacheObject(to: Object, from: Object, objType?: string): void;
export declare function mergeUserCacheObject(to: Object, from: Object): void;
export declare function mergeBoardCacheObject(to: Object, from: Object): void;
export declare function mergeGroupCacheObject(to: Object, from: Object): void;
export declare function mergeSessionCacheObject(to: Object, from: Object): void;
//# sourceMappingURL=merge.d.ts.map