import { Group, User } from '@moxo/proto';
type SubscriptionId = string;
interface SubscribeCallback {
    (): void;
}
interface CacheUpdateFunction<T> {
    (arg: T): T;
}
export declare enum CacheType {
    sessionStorage = "sessionStorage",
    localStorage = "localStorage",
    indexDB = "indexDB"
}
interface CacheOption {
    cacheType: CacheType;
    cacheKey: string;
    dbName?: string;
    version?: number;
    saveOnUpdate?: boolean;
}
export interface ICache<T> {
    subscribe(fn: SubscribeCallback): SubscriptionId;
    unsubscribe(id: SubscriptionId): void;
    update(fn: CacheUpdateFunction<T>): void;
    clear(): void;
}
declare const SysCacheSymbol: unique symbol;
export declare class SysCache<T> implements ICache<T> {
    private subscriber;
    private [SysCacheSymbol];
    private option;
    private db;
    private inited;
    constructor(option?: CacheOption);
    init(): Promise<void>;
    clear(): void;
    subscribe(fn: SubscribeCallback): SubscriptionId;
    unsubscribe(id: SubscriptionId): void;
    update(fn: CacheUpdateFunction<T>): Promise<void>;
}
declare const UserCache: SysCache<User>;
declare const GroupCache: SysCache<Group>;
export interface AppConfig {
    language: string;
    theme: string;
}
export { UserCache, GroupCache };
//# sourceMappingURL=cache.d.ts.map